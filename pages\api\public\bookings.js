import { getAdminClient } from '@/lib/supabase';
import { sendBookingNotification } from '@/lib/notifications';
import {
  validateBookingData,
  validateCustomerData,
  sanitizeBookingData,
  sanitizeCustomerData,
  checkBookingConflicts,
  formatValidationErrors,
  formatSuccessResponse
} from '@/lib/booking-validation';

/**
 * Public API endpoint for handling booking submissions from the public website
 * This endpoint uses service_role key to bypass RLS policies
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version');

  // Handle OPTIONS request
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Route request to appropriate handler based on HTTP method
  if (req.method === 'GET') {
    return await handleGetBookingStatus(req, res);
  } else if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      name,
      email,
      phone,
      date,
      time,
      location,
      message,
      service,
      option,
      customerId,
      marketingConsent
    } = req.body;

    // Validate required fields
    if (!name || !email || !phone || !date || !time || !location || !service || !option) {
      return res.status(400).json(formatValidationErrors(['Missing required fields: name, email, phone, date, time, location, service, and option are all required']));
    }

    // Sanitize customer data
    const sanitizedCustomerData = sanitizeCustomerData({
      name,
      email,
      phone,
      marketing_consent: marketingConsent
    });

    // Validate customer data
    const customerValidation = validateCustomerData(sanitizedCustomerData);
    if (!customerValidation.isValid) {
      return res.status(400).json(formatValidationErrors(customerValidation.errors, customerValidation.warnings));
    }

    console.log('Received booking request:', { name, email, service: service.name, date, time });

    // Use provided customerId or check if customer already exists
    let bookingCustomerId = customerId;

    // Get admin client
    const adminClient = getAdminClient();
    if (!adminClient) {
      console.error("Supabase admin client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }

    if (!bookingCustomerId) {
      const { data: existingCustomer, error: customerCheckError } = await adminClient
        .from('customers')
        .select('id')
        .eq('email', email)
        .single();

      if (customerCheckError && customerCheckError.code !== 'PGRST116') {
        console.error('Error checking for existing customer:', customerCheckError);
      }

      let customerId;

      // If customer doesn't exist, create a new one
      if (!existingCustomer) {
        console.log('Creating new customer:', name, email);
        const { data: newCustomer, error: createCustomerError } = await adminClient
          .from('customers')
          .insert([{
            name,
            email,
            phone,
            marketing_consent: marketingConsent !== undefined ? marketingConsent : true // Use provided preference or default
          }])
          .select();

        if (createCustomerError) {
          console.error('Error creating customer:', createCustomerError);
          throw createCustomerError;
        }

        customerId = newCustomer[0].id;
        console.log('Created new customer with ID:', customerId);
      } else {
        customerId = existingCustomer.id;
        console.log('Using existing customer with ID:', customerId);

        // Update existing customer's marketing preferences if provided
        if (marketingConsent !== undefined) {
          await adminClient
            .from('customers')
            .update({ marketing_consent: marketingConsent })
            .eq('id', customerId);
        }
      }

      bookingCustomerId = customerId;
    }

    // Clean the service name by removing emoji prefixes
    const cleanServiceName = service.name.replace(/^🎨 |^✨ |^💇 |^🎭 |^🚗 |^🅿️ |^📸 /, '').trim();

    let serviceId;
    let serviceData;

    // First try to find the service by ID if it exists
    if (service.id) {
      const { data: serviceById, error: serviceByIdError } = await adminClient
        .from('services')
        .select('id, name, duration')
        .eq('id', service.id)
        .single();

      if (!serviceByIdError && serviceById) {
        serviceData = [serviceById];
        console.log('Found service by ID:', serviceById);
      }
    }

    // If not found by ID, try to find by name
    if (!serviceData) {
      const { data: serviceByName, error: serviceByNameError } = await adminClient
        .from('services')
        .select('id, name, duration')
        .or(`name.ilike.%${cleanServiceName}%,name.ilike.%${service.name}%`)
        .limit(1);

      if (!serviceByNameError && serviceByName && serviceByName.length > 0) {
        serviceData = serviceByName;
        console.log('Found service by name:', serviceByName[0]);
      }
    }

    // If service still not found, create it
    if (!serviceData || serviceData.length === 0) {
      console.log('Service not found, creating new service with clean name:', cleanServiceName);

      const { data: newService, error: createServiceError } = await adminClient
        .from('services')
        .insert([{
          name: cleanServiceName,
          duration: option.hours * 60, // Convert hours to minutes
          price: option.price,
          color: '#6a0dad' // Default color
        }])
        .select();

      if (createServiceError) {
        console.error('Error creating service:', createServiceError);
        throw createServiceError;
      }

      serviceData = newService;
      console.log('Created new service:', newService[0]);
    }

    serviceId = serviceData[0].id;
    console.log('Using service ID:', serviceId);

    // Create date - handle both local time and UTC appropriately
    const dateStr = `${date}T${time}:00`;
    const startTime = new Date(dateStr);

    console.log('Date string created:', dateStr);
    console.log('Created start time:', startTime.toISOString());

    // Calculate end time based on service duration or option hours
    const endTime = new Date(startTime);
    endTime.setMinutes(endTime.getMinutes() + (option.hours * 60)); // Convert hours to minutes
    console.log('Created end time:', endTime.toISOString());

    // Prepare booking data
    const rawBookingData = {
      customer_id: bookingCustomerId,
      service_id: serviceId,
      start_time: startTime.toISOString(),
      end_time: endTime.toISOString(),
      status: service.bookingType === 'Request to Book' ? 'pending' : 'confirmed',
      location,
      notes: message || ''
    };

    // Sanitize booking data
    const bookingRecord = sanitizeBookingData(rawBookingData);

    // Validate booking data
    const bookingValidation = validateBookingData(bookingRecord);
    if (!bookingValidation.isValid) {
      return res.status(400).json(formatValidationErrors(bookingValidation.errors, bookingValidation.warnings));
    }

    // Check for booking conflicts
    const conflictCheck = await checkBookingConflicts(adminClient, bookingRecord);
    if (conflictCheck.hasConflicts) {
      return res.status(409).json({
        error: 'Booking conflict detected',
        message: `This time slot conflicts with ${conflictCheck.conflictCount} existing booking(s)`,
        conflicts: conflictCheck.conflicts
      });
    }

    console.log('Inserting validated booking record:', bookingRecord);

    // Create the booking
    const { data: booking, error: bookingError } = await adminClient
      .from('bookings')
      .insert([bookingRecord])
      .select();

    if (bookingError) {
      console.error('Error creating booking:', bookingError);
      throw bookingError;
    }

    console.log('Successfully created booking:', booking[0]);

    // Get customer details for notification
    const { data: customerData } = await adminClient
      .from('customers')
      .select('name, email, phone, marketing_consent')
      .eq('id', bookingCustomerId)
      .single();

    // Send notification
    await sendBookingNotification({
      bookingId: booking[0].id,
      customerId: bookingCustomerId,
      customerEmail: customerData?.email || email,
      customerName: customerData?.name || name,
      customerPhone: customerData?.phone || phone,
      status: booking[0].status,
      startTime: startTime.toISOString(),
      serviceName: service.name,
      location,
      marketingConsent: customerData?.marketing_consent
    });

    // Return success response
    const successMessage = service.bookingType === 'Request to Book'
      ? 'Your booking request has been submitted. We will contact you shortly to confirm.'
      : 'Your booking has been confirmed. A confirmation email has been sent to your email address.';

    return res.status(201).json(formatSuccessResponse({
      booking: booking[0],
      customer: customerData,
      service: { id: serviceId, name: service.name }
    }, successMessage));
  } catch (error) {
    console.error('Booking Error Details:', {
      error: {
        message: error.message,
        stack: error.stack,
        code: error.code
      },
      request: {
        body: req.body,
        headers: req.headers
      },
      timestamp: new Date().toISOString()
    });
    return res.status(500).json({
      error: 'Failed to create booking',
      details: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
}
