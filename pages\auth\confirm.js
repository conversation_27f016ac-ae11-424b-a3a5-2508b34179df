import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import supabase from '@/lib/supabase'
import styles from '@/styles/admin/Login.module.css'

export default function ConfirmEmail() {
  const [message, setMessage] = useState('Verifying your email...')
  const [error, setError] = useState(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    // Get the hash fragment from the URL
    const hash = window.location.hash

    if (!hash) {
      setError('Invalid confirmation link')
      setLoading(false)
      return
    }

    const handleEmailConfirmation = async () => {
      try {
        // Use Supabase client directly
        const client = supabase;

        // The hash contains the access token and refresh token
        // Supabase will automatically handle the email confirmation
        const { data, error } = await client.auth.getSession()

        if (error) {
          throw error
        }

        if (data?.session) {
          setMessage('<PERSON><PERSON> confirmed successfully! Redirecting to login...')

          // Redirect to login after 3 seconds
          setTimeout(() => {
            router.push('/admin/login')
          }, 3000)
        } else {
          setError('Unable to confirm email. Please try again or contact support.')
        }
      } catch (error) {
        console.error('Error confirming email:', error)
        setError(error.message || 'An error occurred during email confirmation')
      } finally {
        setLoading(false)
      }
    }

    handleEmailConfirmation()
  }, [router])

  return (
    <>
      <Head>
        <title>Confirm Email | OceanSoulSparkles</title>
        <meta name="description" content="Confirm your email for OceanSoulSparkles" />
      </Head>

      <div className={styles.loginContainer}>
        <div className={styles.loginCard}>
          <div className={styles.logoContainer}>
            <img
              src="images\bannerlogo.PNG"
              alt="OceanSoulSparkles Logo"
              className={styles.logo}
            />
          </div>

          <h1 className={styles.title}>Email Confirmation</h1>

          {loading && (
            <div className={styles.loading}>
              <p>{message}</p>
              <div className={styles.spinner}></div>
            </div>
          )}

          {error && !loading && (
            <div className={styles.error}>{error}</div>
          )}

          {!loading && !error && (
            <div className={styles.success}>{message}</div>
          )}

          <div className={styles.backToSite}>
            <Link href="/admin/login" className={styles.link}>
              Go to Login
            </Link>
          </div>
        </div>
      </div>
    </>
  )
}
