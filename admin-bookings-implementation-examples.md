# Admin Bookings Implementation Examples

## 1. Enhanced Filtering Component

### Component Structure
```jsx
// components/admin/BookingFilters.js
import { useState, useEffect } from 'react';
import styles from '@/styles/admin/BookingFilters.module.css';

export default function BookingFilters({ onFiltersChange, customers, services }) {
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    service: 'all',
    customer: 'all',
    dateRange: 'this_week',
    customStartDate: '',
    customEndDate: '',
    location: ''
  });

  const dateRangePresets = [
    { value: 'today', label: 'Today' },
    { value: 'tomorrow', label: 'Tomorrow' },
    { value: 'this_week', label: 'This Week' },
    { value: 'next_week', label: 'Next Week' },
    { value: 'this_month', label: 'This Month' },
    { value: 'custom', label: 'Custom Range' }
  ];

  const statusOptions = [
    { value: 'all', label: 'All Statuses' },
    { value: 'pending', label: 'Pending' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
    { value: 'canceled', label: 'Canceled' },
    { value: 'no_show', label: 'No Show' }
  ];

  return (
    <div className={styles.filtersContainer}>
      <div className={styles.filtersRow}>
        {/* Search Input */}
        <div className={styles.filterGroup}>
          <label>Search</label>
          <input
            type="text"
            placeholder="Customer name, email, or booking ID..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className={styles.searchInput}
          />
        </div>

        {/* Status Filter */}
        <div className={styles.filterGroup}>
          <label>Status</label>
          <select
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
            className={styles.select}
          >
            {statusOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Service Filter */}
        <div className={styles.filterGroup}>
          <label>Service</label>
          <select
            value={filters.service}
            onChange={(e) => handleFilterChange('service', e.target.value)}
            className={styles.select}
          >
            <option value="all">All Services</option>
            {services.map(service => (
              <option key={service.id} value={service.id}>
                {service.name}
              </option>
            ))}
          </select>
        </div>

        {/* Date Range Filter */}
        <div className={styles.filterGroup}>
          <label>Date Range</label>
          <select
            value={filters.dateRange}
            onChange={(e) => handleFilterChange('dateRange', e.target.value)}
            className={styles.select}
          >
            {dateRangePresets.map(preset => (
              <option key={preset.value} value={preset.value}>
                {preset.label}
              </option>
            ))}
          </select>
        </div>

        {/* Clear Filters Button */}
        <div className={styles.filterGroup}>
          <button
            onClick={clearFilters}
            className={styles.clearButton}
          >
            Clear Filters
          </button>
        </div>
      </div>

      {/* Custom Date Range (shown when 'custom' is selected) */}
      {filters.dateRange === 'custom' && (
        <div className={styles.customDateRow}>
          <div className={styles.filterGroup}>
            <label>Start Date</label>
            <input
              type="date"
              value={filters.customStartDate}
              onChange={(e) => handleFilterChange('customStartDate', e.target.value)}
              className={styles.dateInput}
            />
          </div>
          <div className={styles.filterGroup}>
            <label>End Date</label>
            <input
              type="date"
              value={filters.customEndDate}
              onChange={(e) => handleFilterChange('customEndDate', e.target.value)}
              className={styles.dateInput}
            />
          </div>
        </div>
      )}

      {/* Active Filters Display */}
      {hasActiveFilters() && (
        <div className={styles.activeFilters}>
          <span className={styles.activeFiltersLabel}>Active Filters:</span>
          {renderActiveFilters()}
        </div>
      )}
    </div>
  );
}
```

## 2. Bulk Operations Component

### Bulk Actions Toolbar
```jsx
// components/admin/BulkActionsToolbar.js
import { useState } from 'react';
import styles from '@/styles/admin/BulkActionsToolbar.module.css';

export default function BulkActionsToolbar({ 
  selectedBookings, 
  onBulkAction, 
  onClearSelection 
}) {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [pendingAction, setPendingAction] = useState(null);

  const bulkActions = [
    { value: 'confirm', label: 'Confirm Selected', icon: '✓' },
    { value: 'cancel', label: 'Cancel Selected', icon: '✕' },
    { value: 'reschedule', label: 'Bulk Reschedule', icon: '📅' },
    { value: 'export', label: 'Export Selected', icon: '📊' },
    { value: 'send_reminder', label: 'Send Reminders', icon: '📧' }
  ];

  const handleBulkAction = (action) => {
    if (['cancel', 'confirm'].includes(action)) {
      setPendingAction(action);
      setShowConfirmation(true);
    } else {
      onBulkAction(action, selectedBookings);
    }
  };

  return (
    <div className={styles.toolbar}>
      <div className={styles.selectionInfo}>
        <span className={styles.count}>
          {selectedBookings.length} booking{selectedBookings.length !== 1 ? 's' : ''} selected
        </span>
        <button
          onClick={onClearSelection}
          className={styles.clearSelection}
        >
          Clear Selection
        </button>
      </div>

      <div className={styles.actions}>
        {bulkActions.map(action => (
          <button
            key={action.value}
            onClick={() => handleBulkAction(action.value)}
            className={styles.actionButton}
            disabled={selectedBookings.length === 0}
          >
            <span className={styles.actionIcon}>{action.icon}</span>
            {action.label}
          </button>
        ))}
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className={styles.confirmationModal}>
          <div className={styles.modalContent}>
            <h3>Confirm Bulk Action</h3>
            <p>
              Are you sure you want to {pendingAction} {selectedBookings.length} booking(s)?
              This action cannot be undone.
            </p>
            <div className={styles.modalActions}>
              <button
                onClick={() => setShowConfirmation(false)}
                className={styles.cancelButton}
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  onBulkAction(pendingAction, selectedBookings);
                  setShowConfirmation(false);
                  setPendingAction(null);
                }}
                className={styles.confirmButton}
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
```

## 3. Enhanced Booking Details Component

### Comprehensive Booking View
```jsx
// components/admin/EnhancedBookingDetails.js
import { useState, useEffect } from 'react';
import CustomerBookingHistory from './CustomerBookingHistory';
import BookingStatusHistory from './BookingStatusHistory';
import BookingPaymentInfo from './BookingPaymentInfo';
import BookingCommunications from './BookingCommunications';
import styles from '@/styles/admin/EnhancedBookingDetails.module.css';

export default function EnhancedBookingDetails({ 
  booking, 
  onClose, 
  onEdit, 
  onUpdate 
}) {
  const [activeTab, setActiveTab] = useState('details');
  const [customerHistory, setCustomerHistory] = useState([]);
  const [paymentInfo, setPaymentInfo] = useState(null);
  const [communications, setCommunications] = useState([]);

  const tabs = [
    { id: 'details', label: 'Booking Details', icon: '📋' },
    { id: 'customer', label: 'Customer Info', icon: '👤' },
    { id: 'history', label: 'Status History', icon: '📊' },
    { id: 'payment', label: 'Payment', icon: '💳' },
    { id: 'communications', label: 'Communications', icon: '💬' }
  ];

  return (
    <div className={styles.enhancedDetails}>
      <div className={styles.header}>
        <div className={styles.bookingTitle}>
          <h2>Booking #{booking.id.slice(-8)}</h2>
          <div className={styles.bookingMeta}>
            <span className={`${styles.status} ${styles[booking.status]}`}>
              {booking.status.replace('_', ' ').toUpperCase()}
            </span>
            <span className={styles.service}>
              {booking.serviceName}
            </span>
          </div>
        </div>
        
        <div className={styles.headerActions}>
          <button onClick={onEdit} className={styles.editButton}>
            Edit Booking
          </button>
          <button onClick={onClose} className={styles.closeButton}>
            ×
          </button>
        </div>
      </div>

      {/* Quick Actions Bar */}
      <div className={styles.quickActions}>
        <button className={styles.quickAction}>
          📧 Send Reminder
        </button>
        <button className={styles.quickAction}>
          📞 Call Customer
        </button>
        <button className={styles.quickAction}>
          📅 Reschedule
        </button>
        <button className={styles.quickAction}>
          💰 Add Payment
        </button>
      </div>

      {/* Tab Navigation */}
      <div className={styles.tabNavigation}>
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`${styles.tab} ${activeTab === tab.id ? styles.active : ''}`}
          >
            <span className={styles.tabIcon}>{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className={styles.tabContent}>
        {activeTab === 'details' && (
          <BookingDetailsTab booking={booking} />
        )}
        {activeTab === 'customer' && (
          <CustomerInfoTab 
            customer={booking.customer} 
            bookingHistory={customerHistory}
          />
        )}
        {activeTab === 'history' && (
          <BookingStatusHistory bookingId={booking.id} />
        )}
        {activeTab === 'payment' && (
          <BookingPaymentInfo 
            bookingId={booking.id}
            paymentInfo={paymentInfo}
          />
        )}
        {activeTab === 'communications' && (
          <BookingCommunications 
            bookingId={booking.id}
            customerId={booking.customer_id}
            communications={communications}
          />
        )}
      </div>
    </div>
  );
}
```

## 4. Recurring Booking Management

### Recurring Booking Form
```jsx
// components/admin/RecurringBookingForm.js
import { useState } from 'react';
import styles from '@/styles/admin/RecurringBookingForm.module.css';

export default function RecurringBookingForm({ 
  booking, 
  onSave, 
  onCancel 
}) {
  const [recurringData, setRecurringData] = useState({
    isRecurring: false,
    pattern: 'weekly',
    interval: 1,
    endType: 'date',
    endDate: '',
    occurrences: 10,
    daysOfWeek: [],
    monthlyType: 'date' // 'date' or 'day'
  });

  const patterns = [
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' }
  ];

  const daysOfWeek = [
    { value: 'monday', label: 'Mon' },
    { value: 'tuesday', label: 'Tue' },
    { value: 'wednesday', label: 'Wed' },
    { value: 'thursday', label: 'Thu' },
    { value: 'friday', label: 'Fri' },
    { value: 'saturday', label: 'Sat' },
    { value: 'sunday', label: 'Sun' }
  ];

  return (
    <div className={styles.recurringForm}>
      <div className={styles.recurringToggle}>
        <label className={styles.toggleLabel}>
          <input
            type="checkbox"
            checked={recurringData.isRecurring}
            onChange={(e) => setRecurringData({
              ...recurringData,
              isRecurring: e.target.checked
            })}
          />
          Make this a recurring booking
        </label>
      </div>

      {recurringData.isRecurring && (
        <div className={styles.recurringOptions}>
          {/* Pattern Selection */}
          <div className={styles.formGroup}>
            <label>Repeat Pattern</label>
            <select
              value={recurringData.pattern}
              onChange={(e) => setRecurringData({
                ...recurringData,
                pattern: e.target.value
              })}
              className={styles.select}
            >
              {patterns.map(pattern => (
                <option key={pattern.value} value={pattern.value}>
                  {pattern.label}
                </option>
              ))}
            </select>
          </div>

          {/* Interval */}
          <div className={styles.formGroup}>
            <label>Every</label>
            <div className={styles.intervalGroup}>
              <input
                type="number"
                min="1"
                max="12"
                value={recurringData.interval}
                onChange={(e) => setRecurringData({
                  ...recurringData,
                  interval: parseInt(e.target.value)
                })}
                className={styles.numberInput}
              />
              <span className={styles.intervalLabel}>
                {recurringData.pattern === 'daily' && 'day(s)'}
                {recurringData.pattern === 'weekly' && 'week(s)'}
                {recurringData.pattern === 'monthly' && 'month(s)'}
              </span>
            </div>
          </div>

          {/* Days of Week (for weekly pattern) */}
          {recurringData.pattern === 'weekly' && (
            <div className={styles.formGroup}>
              <label>Days of Week</label>
              <div className={styles.daysOfWeek}>
                {daysOfWeek.map(day => (
                  <label key={day.value} className={styles.dayLabel}>
                    <input
                      type="checkbox"
                      checked={recurringData.daysOfWeek.includes(day.value)}
                      onChange={(e) => {
                        const days = e.target.checked
                          ? [...recurringData.daysOfWeek, day.value]
                          : recurringData.daysOfWeek.filter(d => d !== day.value);
                        setRecurringData({
                          ...recurringData,
                          daysOfWeek: days
                        });
                      }}
                    />
                    {day.label}
                  </label>
                ))}
              </div>
            </div>
          )}

          {/* End Options */}
          <div className={styles.formGroup}>
            <label>End</label>
            <div className={styles.endOptions}>
              <label className={styles.radioLabel}>
                <input
                  type="radio"
                  name="endType"
                  value="date"
                  checked={recurringData.endType === 'date'}
                  onChange={(e) => setRecurringData({
                    ...recurringData,
                    endType: e.target.value
                  })}
                />
                On date
              </label>
              {recurringData.endType === 'date' && (
                <input
                  type="date"
                  value={recurringData.endDate}
                  onChange={(e) => setRecurringData({
                    ...recurringData,
                    endDate: e.target.value
                  })}
                  className={styles.dateInput}
                />
              )}

              <label className={styles.radioLabel}>
                <input
                  type="radio"
                  name="endType"
                  value="occurrences"
                  checked={recurringData.endType === 'occurrences'}
                  onChange={(e) => setRecurringData({
                    ...recurringData,
                    endType: e.target.value
                  })}
                />
                After
              </label>
              {recurringData.endType === 'occurrences' && (
                <div className={styles.occurrencesGroup}>
                  <input
                    type="number"
                    min="1"
                    max="100"
                    value={recurringData.occurrences}
                    onChange={(e) => setRecurringData({
                      ...recurringData,
                      occurrences: parseInt(e.target.value)
                    })}
                    className={styles.numberInput}
                  />
                  <span>occurrences</span>
                </div>
              )}
            </div>
          </div>

          {/* Preview */}
          <div className={styles.preview}>
            <h4>Preview</h4>
            <div className={styles.previewText}>
              {generatePreviewText(recurringData)}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
```

## 5. Analytics Integration

### Booking Analytics Widget
```jsx
// components/admin/BookingAnalyticsWidget.js
import { useState, useEffect } from 'react';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import styles from '@/styles/admin/BookingAnalyticsWidget.module.css';

export default function BookingAnalyticsWidget({ dateRange = '7d' }) {
  const [analytics, setAnalytics] = useState({
    totalBookings: 0,
    confirmedBookings: 0,
    canceledBookings: 0,
    revenue: 0,
    popularServices: [],
    bookingTrends: [],
    timeSlotPopularity: []
  });

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAnalytics();
  }, [dateRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/analytics/bookings?range=${dateRange}`);
      const data = await response.json();
      setAnalytics(data);
    } catch (error) {
      console.error('Error fetching booking analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className={styles.loading}>Loading analytics...</div>;
  }

  return (
    <div className={styles.analyticsWidget}>
      <div className={styles.header}>
        <h3>Booking Analytics</h3>
        <select
          value={dateRange}
          onChange={(e) => setDateRange(e.target.value)}
          className={styles.rangeSelector}
        >
          <option value="7d">Last 7 Days</option>
          <option value="30d">Last 30 Days</option>
          <option value="90d">Last 90 Days</option>
        </select>
      </div>

      {/* Key Metrics */}
      <div className={styles.metricsGrid}>
        <div className={styles.metric}>
          <div className={styles.metricValue}>{analytics.totalBookings}</div>
          <div className={styles.metricLabel}>Total Bookings</div>
        </div>
        <div className={styles.metric}>
          <div className={styles.metricValue}>{analytics.confirmedBookings}</div>
          <div className={styles.metricLabel}>Confirmed</div>
        </div>
        <div className={styles.metric}>
          <div className={styles.metricValue}>${analytics.revenue}</div>
          <div className={styles.metricLabel}>Revenue</div>
        </div>
        <div className={styles.metric}>
          <div className={styles.metricValue}>
            {((analytics.confirmedBookings / analytics.totalBookings) * 100).toFixed(1)}%
          </div>
          <div className={styles.metricLabel}>Confirmation Rate</div>
        </div>
      </div>

      {/* Charts */}
      <div className={styles.chartsGrid}>
        <div className={styles.chartContainer}>
          <h4>Booking Trends</h4>
          <Line data={prepareBookingTrendsData(analytics.bookingTrends)} />
        </div>
        <div className={styles.chartContainer}>
          <h4>Popular Services</h4>
          <Doughnut data={prepareServicesData(analytics.popularServices)} />
        </div>
      </div>
    </div>
  );
}
```

This implementation plan provides concrete examples of how to enhance the admin booking system with improved functionality, better user experience, and comprehensive analytics integration.
