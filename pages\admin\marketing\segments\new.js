import { useState, useCallback } from 'react'
import { useRouter } from 'next/router'
import AdminLayout from '@/components/admin/AdminLayout'
import SegmentBuilder from '@/components/admin/marketing/SegmentBuilder'
import styles from '@/styles/admin/marketing/SegmentForm.module.css'

export default function NewSegment() {
  const router = useRouter()
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [segmentQuery, setSegmentQuery] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [successMessage, setSuccessMessage] = useState(null)

  // Handle segment query change - memoized to prevent infinite re-renders
  const handleSegmentQueryChange = useCallback((query) => {
    setSegmentQuery(query)
  }, [])

  // Handle segment preview - memoized to prevent unnecessary re-renders
  const handleSegmentPreview = useCallback(async (query) => {
    try {
      const response = await fetch('/api/marketing/segment-builder/preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ segment_query: query })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to preview segment')
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('Error previewing segment:', error)
      setError(error.message)
      return { customers: [], total: 0 }
    }
  }, [setError])

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    setSuccessMessage(null)

    try {
      // Validate form
      if (!name) {
        throw new Error('Segment name is required')
      }

      if (!segmentQuery || !segmentQuery.groups || segmentQuery.groups.length === 0) {
        throw new Error('Segment conditions are required')
      }

      // Create segment
      const response = await fetch('/api/marketing/segments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name,
          description,
          segment_query: segmentQuery
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create segment')
      }

      const data = await response.json()
      setSuccessMessage('Segment created successfully')

      // Redirect to segment list after a short delay
      setTimeout(() => {
        router.push('/admin/marketing/segments')
      }, 1500)
    } catch (error) {
      console.error('Error creating segment:', error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <AdminLayout>
      <div className={styles.segmentForm}>
        <div className={styles.header}>
          <h2>Create Customer Segment</h2>
          <button
            className={styles.cancelButton}
            onClick={() => router.push('/admin/marketing/segments')}
            disabled={loading}
          >
            Cancel
          </button>
        </div>

        {error && (
          <div className={styles.error}>
            Error: {error}
          </div>
        )}

        {successMessage && (
          <div className={styles.success}>
            {successMessage}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className={styles.formSection}>
            <div className={styles.formGroup}>
              <label htmlFor="name">Segment Name *</label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="e.g., High Value Customers"
                className={styles.input}
                disabled={loading}
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="description">Description</label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe the purpose of this segment"
                className={styles.textarea}
                disabled={loading}
                rows={3}
              />
            </div>
          </div>

          <div className={styles.formSection}>
            <SegmentBuilder
              onChange={handleSegmentQueryChange}
              onPreview={handleSegmentPreview}
            />
          </div>

          <div className={styles.formActions}>
            <button
              type="submit"
              className={styles.submitButton}
              disabled={loading}
            >
              {loading ? 'Creating...' : 'Create Segment'}
            </button>
          </div>
        </form>
      </div>
    </AdminLayout>
  )
}
