.filtersContainer {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filtersHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.filtersTitle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filtersTitle h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.activeCount {
  background: #3788d8;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.filtersActions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.toggleButton {
  background: #3788d8;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.toggleButton:hover {
  background: #2c6bb8;
}

.clearButton {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.clearButton:hover {
  background: #c0392b;
}

.filtersContent {
  padding: 20px;
}

.filtersRow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.customDateRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.filterGroup label {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.searchInput,
.textInput,
.select,
.dateInput {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
  background: white;
}

.searchInput:focus,
.textInput:focus,
.select:focus,
.dateInput:focus {
  outline: none;
  border-color: #3788d8;
  box-shadow: 0 0 0 3px rgba(55, 136, 216, 0.1);
}

.searchInput {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cpath d='m21 21-4.35-4.35'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: 12px center;
  padding-left: 40px;
}

.select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6,9 12,15 18,9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  padding-right: 40px;
  appearance: none;
}

.activeFilters {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

.activeFiltersLabel {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 8px;
  display: block;
}

.activeFiltersList {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.activeFilter {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: #3788d8;
  color: white;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.activeFilter button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  padding: 0;
  margin-left: 4px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.activeFilter button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .filtersHeader {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .filtersActions {
    justify-content: center;
  }

  .filtersRow {
    grid-template-columns: 1fr;
  }

  .customDateRow {
    grid-template-columns: 1fr;
  }

  .activeFiltersList {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .filtersContainer {
    margin: 0 -10px 20px -10px;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .filtersHeader {
    border-radius: 0;
  }

  .filtersContent {
    padding: 16px;
  }

  .filtersActions {
    flex-direction: column;
    gap: 8px;
  }

  .toggleButton,
  .clearButton {
    width: 100%;
    text-align: center;
  }
}

/* Animation for expanding/collapsing */
.filtersContent {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state */
.filterGroup.loading {
  opacity: 0.6;
  pointer-events: none;
}

.filterGroup.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 12px;
  width: 16px;
  height: 16px;
  border: 2px solid #ddd;
  border-top: 2px solid #3788d8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Focus indicators for accessibility */
.toggleButton:focus,
.clearButton:focus,
.searchInput:focus,
.textInput:focus,
.select:focus,
.dateInput:focus {
  outline: 2px solid #3788d8;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .filtersContainer {
    border: 2px solid #000;
  }
  
  .activeFilter {
    background: #000;
    color: #fff;
  }
  
  .toggleButton,
  .clearButton {
    border: 2px solid #000;
  }
}
