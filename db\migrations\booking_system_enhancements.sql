-- Booking System Enhancements Migration
-- This script adds new tables and columns to support enhanced booking management

-- =============================================
-- ENHANCED BOOKINGS TABLE
-- =============================================

-- Add new columns to existing bookings table
ALTER TABLE public.bookings 
ADD COLUMN IF NOT EXISTS booking_source TEXT DEFAULT 'admin',
ADD COLUMN IF NOT EXISTS estimated_revenue DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS actual_revenue DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS preparation_time INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS cleanup_time INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS priority_level INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS internal_notes TEXT,
ADD COLUMN IF NOT EXISTS customer_notes TEXT,
ADD COLUMN IF NOT EXISTS booking_reference TEXT UNIQUE,
ADD COLUMN IF NOT EXISTS requires_confirmation BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS confirmation_sent_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS reminder_sent_at TIMESTAMPTZ;

-- Create indexes for improved performance
CREATE INDEX IF NOT EXISTS bookings_booking_source_idx ON public.bookings(booking_source);
CREATE INDEX IF NOT EXISTS bookings_priority_level_idx ON public.bookings(priority_level);
CREATE INDEX IF NOT EXISTS bookings_booking_reference_idx ON public.bookings(booking_reference);
CREATE INDEX IF NOT EXISTS bookings_customer_search_idx ON public.bookings USING gin(to_tsvector('english', customer_notes || ' ' || internal_notes));

-- =============================================
-- BOOKING TEMPLATES TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.booking_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  service_id UUID REFERENCES public.services(id),
  duration_override INTEGER, -- Override service default duration
  default_location TEXT,
  notes_template TEXT,
  preparation_time INTEGER DEFAULT 0,
  cleanup_time INTEGER DEFAULT 0,
  price_override DECIMAL(10,2), -- Override service default price
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS booking_templates_service_id_idx ON public.booking_templates(service_id);
CREATE INDEX IF NOT EXISTS booking_templates_is_active_idx ON public.booking_templates(is_active);

-- =============================================
-- CUSTOMER BOOKING PREFERENCES TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.customer_booking_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
  preferred_time_slots JSONB, -- Store preferred times as JSON
  preferred_services UUID[], -- Array of service IDs
  avoided_time_slots JSONB, -- Times to avoid
  communication_preferences JSONB, -- Email, SMS, phone preferences
  special_requirements TEXT,
  accessibility_needs TEXT,
  preferred_staff_members UUID[], -- Future: staff preferences
  booking_frequency TEXT, -- 'weekly', 'monthly', 'occasional'
  average_advance_booking INTEGER, -- Days in advance they typically book
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(customer_id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS customer_preferences_customer_id_idx ON public.customer_booking_preferences(customer_id);
CREATE INDEX IF NOT EXISTS customer_preferences_preferred_services_idx ON public.customer_booking_preferences USING gin(preferred_services);

-- =============================================
-- BOOKING CONFLICTS TABLE (Enhanced)
-- =============================================

-- Drop existing table if it exists and recreate with enhancements
DROP TABLE IF EXISTS public.booking_conflicts;

CREATE TABLE public.booking_conflicts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
  conflicting_booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
  conflict_type TEXT NOT NULL CHECK (conflict_type IN ('time_overlap', 'resource_conflict', 'staff_conflict', 'location_conflict')),
  severity TEXT NOT NULL DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  resolved BOOLEAN DEFAULT FALSE,
  resolution_type TEXT, -- 'rescheduled', 'canceled', 'ignored', 'manual_override'
  resolution_notes TEXT,
  auto_detected BOOLEAN DEFAULT TRUE,
  resolved_by UUID REFERENCES auth.users(id),
  resolved_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS booking_conflicts_booking_id_idx ON public.booking_conflicts(booking_id);
CREATE INDEX IF NOT EXISTS booking_conflicts_resolved_idx ON public.booking_conflicts(resolved);
CREATE INDEX IF NOT EXISTS booking_conflicts_severity_idx ON public.booking_conflicts(severity);

-- =============================================
-- BOOKING COMMUNICATIONS TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.booking_communications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
  communication_type TEXT NOT NULL CHECK (communication_type IN ('email', 'sms', 'phone', 'in_person', 'system')),
  direction TEXT NOT NULL CHECK (direction IN ('inbound', 'outbound')),
  subject TEXT,
  content TEXT NOT NULL,
  status TEXT DEFAULT 'sent' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'read')),
  sent_by UUID REFERENCES auth.users(id),
  external_id TEXT, -- For tracking with external services (OneSignal, etc.)
  metadata JSONB, -- Store additional communication metadata
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS booking_communications_booking_id_idx ON public.booking_communications(booking_id);
CREATE INDEX IF NOT EXISTS booking_communications_customer_id_idx ON public.booking_communications(customer_id);
CREATE INDEX IF NOT EXISTS booking_communications_type_idx ON public.booking_communications(communication_type);
CREATE INDEX IF NOT EXISTS booking_communications_created_at_idx ON public.booking_communications(created_at);

-- =============================================
-- BOOKING ANALYTICS VIEWS
-- =============================================

-- Create view for booking analytics
CREATE OR REPLACE VIEW public.booking_analytics AS
SELECT 
  DATE_TRUNC('day', start_time) as booking_date,
  COUNT(*) as total_bookings,
  COUNT(*) FILTER (WHERE status = 'confirmed') as confirmed_bookings,
  COUNT(*) FILTER (WHERE status = 'canceled') as canceled_bookings,
  COUNT(*) FILTER (WHERE status = 'completed') as completed_bookings,
  COUNT(*) FILTER (WHERE status = 'no_show') as no_show_bookings,
  SUM(COALESCE(actual_revenue, estimated_revenue, 0)) as total_revenue,
  AVG(COALESCE(actual_revenue, estimated_revenue, 0)) as average_revenue,
  service_id,
  s.name as service_name,
  s.color as service_color
FROM public.bookings b
LEFT JOIN public.services s ON b.service_id = s.id
GROUP BY DATE_TRUNC('day', start_time), service_id, s.name, s.color
ORDER BY booking_date DESC;

-- Create view for popular time slots
CREATE OR REPLACE VIEW public.popular_time_slots AS
SELECT 
  EXTRACT(hour FROM start_time) as hour_of_day,
  EXTRACT(dow FROM start_time) as day_of_week,
  COUNT(*) as booking_count,
  COUNT(*) FILTER (WHERE status = 'confirmed') as confirmed_count,
  ROUND(AVG(COALESCE(actual_revenue, estimated_revenue, 0)), 2) as average_revenue
FROM public.bookings
WHERE start_time >= NOW() - INTERVAL '90 days'
GROUP BY EXTRACT(hour FROM start_time), EXTRACT(dow FROM start_time)
ORDER BY booking_count DESC;

-- =============================================
-- ENHANCED FUNCTIONS
-- =============================================

-- Function to generate booking reference
CREATE OR REPLACE FUNCTION public.generate_booking_reference()
RETURNS TEXT AS $$
DECLARE
  ref TEXT;
  exists_check INTEGER;
BEGIN
  LOOP
    -- Generate reference: OSS + YYYYMMDD + 4 random digits
    ref := 'OSS' || TO_CHAR(NOW(), 'YYYYMMDD') || LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0');
    
    -- Check if reference already exists
    SELECT COUNT(*) INTO exists_check FROM public.bookings WHERE booking_reference = ref;
    
    -- If unique, exit loop
    IF exists_check = 0 THEN
      EXIT;
    END IF;
  END LOOP;
  
  RETURN ref;
END;
$$ LANGUAGE plpgsql;

-- Function to detect booking conflicts
CREATE OR REPLACE FUNCTION public.detect_booking_conflicts(
  p_booking_id UUID,
  p_start_time TIMESTAMPTZ,
  p_end_time TIMESTAMPTZ,
  p_service_id UUID DEFAULT NULL
)
RETURNS TABLE(
  conflicting_booking_id UUID,
  conflict_type TEXT,
  severity TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    b.id as conflicting_booking_id,
    'time_overlap'::TEXT as conflict_type,
    CASE 
      WHEN (p_start_time, p_end_time) OVERLAPS (b.start_time, b.end_time) THEN 'high'::TEXT
      ELSE 'medium'::TEXT
    END as severity
  FROM public.bookings b
  WHERE b.id != COALESCE(p_booking_id, '00000000-0000-0000-0000-000000000000'::UUID)
    AND b.status NOT IN ('canceled', 'completed')
    AND (p_start_time, p_end_time) OVERLAPS (b.start_time, b.end_time);
END;
$$ LANGUAGE plpgsql;

-- Function to update booking analytics
CREATE OR REPLACE FUNCTION public.update_booking_analytics()
RETURNS TRIGGER AS $$
BEGIN
  -- This function can be used to maintain analytics tables
  -- For now, it's a placeholder for future analytics updates
  
  -- Log the change for analytics purposes
  INSERT INTO public.booking_status_history (
    booking_id,
    previous_status,
    new_status,
    notes,
    changed_by
  ) VALUES (
    NEW.id,
    COALESCE(OLD.status, 'new'),
    NEW.status,
    'Booking analytics update',
    auth.uid()
  ) ON CONFLICT DO NOTHING;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- TRIGGERS
-- =============================================

-- Trigger to auto-generate booking reference
CREATE OR REPLACE FUNCTION public.set_booking_reference()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.booking_reference IS NULL THEN
    NEW.booking_reference := public.generate_booking_reference();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS set_booking_reference_trigger ON public.bookings;
CREATE TRIGGER set_booking_reference_trigger
  BEFORE INSERT ON public.bookings
  FOR EACH ROW
  EXECUTE FUNCTION public.set_booking_reference();

-- Trigger to auto-detect conflicts
CREATE OR REPLACE FUNCTION public.auto_detect_conflicts()
RETURNS TRIGGER AS $$
DECLARE
  conflict_record RECORD;
BEGIN
  -- Detect conflicts for new or updated bookings
  FOR conflict_record IN 
    SELECT * FROM public.detect_booking_conflicts(NEW.id, NEW.start_time, NEW.end_time, NEW.service_id)
  LOOP
    INSERT INTO public.booking_conflicts (
      booking_id,
      conflicting_booking_id,
      conflict_type,
      severity,
      auto_detected
    ) VALUES (
      NEW.id,
      conflict_record.conflicting_booking_id,
      conflict_record.conflict_type,
      conflict_record.severity,
      TRUE
    ) ON CONFLICT DO NOTHING;
  END LOOP;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS auto_detect_conflicts_trigger ON public.bookings;
CREATE TRIGGER auto_detect_conflicts_trigger
  AFTER INSERT OR UPDATE OF start_time, end_time ON public.bookings
  FOR EACH ROW
  EXECUTE FUNCTION public.auto_detect_conflicts();

-- =============================================
-- ROW LEVEL SECURITY POLICIES
-- =============================================

-- Enable RLS on new tables
ALTER TABLE public.booking_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_booking_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.booking_communications ENABLE ROW LEVEL SECURITY;

-- Policies for booking_templates
CREATE POLICY "Staff can manage booking templates" ON public.booking_templates
  FOR ALL USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Policies for customer_booking_preferences
CREATE POLICY "Staff can manage customer preferences" ON public.customer_booking_preferences
  FOR ALL USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Policies for booking_communications
CREATE POLICY "Staff can manage booking communications" ON public.booking_communications
  FOR ALL USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- =============================================
-- SAMPLE DATA (Optional)
-- =============================================

-- Insert sample booking templates
INSERT INTO public.booking_templates (name, description, service_id, notes_template, is_active)
SELECT 
  'Standard ' || s.name,
  'Standard booking template for ' || s.name,
  s.id,
  'Please arrive 10 minutes early. Bring comfortable clothing.',
  TRUE
FROM public.services s
WHERE NOT EXISTS (
  SELECT 1 FROM public.booking_templates bt WHERE bt.service_id = s.id
)
LIMIT 5;

-- Update existing bookings with booking references (if they don't have them)
UPDATE public.bookings 
SET booking_reference = public.generate_booking_reference()
WHERE booking_reference IS NULL;

-- =============================================
-- PERFORMANCE OPTIMIZATIONS
-- =============================================

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS bookings_customer_service_date_idx 
ON public.bookings(customer_id, service_id, start_time);

CREATE INDEX IF NOT EXISTS bookings_status_date_idx 
ON public.bookings(status, start_time);

CREATE INDEX IF NOT EXISTS bookings_revenue_date_idx 
ON public.bookings(start_time, actual_revenue, estimated_revenue);

-- Analyze tables for query optimization
ANALYZE public.bookings;
ANALYZE public.booking_templates;
ANALYZE public.customer_booking_preferences;
ANALYZE public.booking_conflicts;
ANALYZE public.booking_communications;
