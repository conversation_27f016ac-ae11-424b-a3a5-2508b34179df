.bookingsPage {
  padding: 0 0 30px;
}

.statsContainer {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.statCard {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.statIcon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background-color: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: #6c757d;
}

.statIcon.confirmed {
  background-color: #d4edda;
  color: #155724;
}

.statIcon.pending {
  background-color: #fff3cd;
  color: #856404;
}

.statIcon.canceled {
  background-color: #f8d7da;
  color: #721c24;
}

.statContent {
  flex: 1;
}

.statContent h3 {
  margin: 0 0 5px;
  font-size: 0.9rem;
  color: #6c757d;
  font-weight: 500;
}

.statValue {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #212529;
}

.actionBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.actionButtons {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filteringIndicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3788d8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.sectionTitle {
  margin: 0;
  font-size: 1.25rem;
  color: #333;
}

.addButton {
  background-color: #6a0dad;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.addButton:hover {
  background-color: #5a0b9d;
}

.contentContainer {
  width: 100%;
}

.calendarContainer {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.noSelection {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  text-align: center;
}

.noSelectionIcon {
  color: #dee2e6;
  margin-bottom: 20px;
}

.noSelection h3 {
  margin: 0 0 10px;
  font-size: 1.1rem;
  color: #495057;
}

.noSelection p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
  max-width: 300px;
  line-height: 1.5;
}

/* Loading and error states */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #6a0dad;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingContainer p {
  margin: 0;
  color: #6c757d;
  font-size: 1rem;
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.errorMessage {
  margin: 0 0 20px;
  color: #dc3545;
  font-size: 1rem;
  font-weight: 500;
}

.retryButton {
  background-color: #6a0dad;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retryButton:hover {
  background-color: #5a0b9d;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .statsContainer {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .statsContainer {
    grid-template-columns: 1fr;
  }

  .actionBar {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
