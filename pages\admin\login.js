import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { signIn } from '@/lib/auth'
import supabase from '@/lib/supabase'
import { toast } from 'react-toastify'
import styles from '@/styles/admin/Login.module.css'

export default function Login() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState(null)
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  // Add script loading for error recovery
  useEffect(() => {
    // Create and append the scripts for error handling and recovery
    const extensionErrorScript = document.createElement('script');
    extensionErrorScript.src = '/scripts/extension-error-suppression.js';
    extensionErrorScript.async = false;

    const whiteScreenRecoveryScript = document.createElement('script');
    whiteScreenRecoveryScript.src = '/scripts/auth-white-screen-recovery.js';
    whiteScreenRecoveryScript.async = false;

    const loginRecoveryScript = document.createElement('script');
    loginRecoveryScript.src = '/scripts/login-error-recovery.js';
    loginRecoveryScript.async = false;

    document.head.appendChild(extensionErrorScript);
    document.head.appendChild(whiteScreenRecoveryScript);
    document.head.appendChild(loginRecoveryScript);

    return () => {
      // Clean up scripts when component unmounts
      if (extensionErrorScript.parentNode) {
        extensionErrorScript.parentNode.removeChild(extensionErrorScript);
      }
      if (loginRecoveryScript.parentNode) {
        loginRecoveryScript.parentNode.removeChild(loginRecoveryScript);
      }
    };
  }, []);

  // Check if user is already authenticated and handle redirect parameters
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check if user is already authenticated
        const { data } = await supabase.auth.getSession()

        if (data?.session?.user) {
          console.log('Login: User already authenticated, redirecting to admin dashboard')
          handleRedirect()
        }
      } catch (err) {
        console.error('Login: Error checking authentication status:', err)
      }
    }

    // Check if there's a redirect URL in the query parameters
    const { redirect } = router.query
    if (redirect) {
      console.log('Login: Found redirect URL in query parameters:', redirect)
      // Store the redirect URL in sessionStorage
      try {
        sessionStorage.setItem('redirect_after_login', redirect)
      } catch (storageError) {
        console.warn('Error storing redirect URL:', storageError)
      }
    }

    checkAuth()
  }, [router.query])

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      // Sign in with email and password
      const { error } = await signIn(email, password)

      if (error) {
        console.error('Login: Authentication error:', error)
        setError(error.message || 'Authentication failed. Please try again.')
        toast.error('Login failed. Please check your credentials and try again.')
        setLoading(false)
        return
      }

      // Show success message
      toast.success('Login successful!')

      // Redirect to the appropriate page
      handleRedirect()
    } catch (err) {
      console.error('Login: Unexpected error during authentication:', err)
      setError('An unexpected error occurred. Please try again.')
      toast.error('Login failed. Please try again later.')
      setLoading(false)
    }
  }

  // Handle redirect after successful login
  const handleRedirect = () => {
    // Check if there's a redirect URL in sessionStorage
    let redirectUrl = '/admin'
    try {
      const storedRedirect = sessionStorage.getItem('redirect_after_login')
      if (storedRedirect) {
        console.log('Login: Found redirect URL in sessionStorage:', storedRedirect)
        redirectUrl = storedRedirect
        // Clear the redirect URL from sessionStorage
        sessionStorage.removeItem('redirect_after_login')
      }
    } catch (storageError) {
      console.warn('Error accessing sessionStorage:', storageError)
    }

    // Redirect to the appropriate page
    console.log('Login: Redirecting to:', redirectUrl)
    router.push(redirectUrl)
  }
  return (
    <>
      <Head>
        <title>Admin Login | OceanSoulSparkles</title>
        <meta name="description" content="OceanSoulSparkles Admin Login" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <noscript>
          <style>{`.js-only { display: none !important; }`}</style>
        </noscript>
      </Head>

      <div id="login-root" className={styles.loginContainer}>
        <div className={styles.loginCard}>
          <div className={styles.logoContainer}>
            <img
              src="images\bannerlogo.PNG"
              alt="OceanSoulSparkles Logo"
              className={styles.logo}
            />
          </div>

          <h1 className={styles.title}>Admin Login</h1>

          {error && <div className={styles.error}>{error}</div>}

          <form onSubmit={handleSubmit} className={styles.form}>
            <div className={styles.formGroup}>
              <label htmlFor="email">Email</label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className={styles.input}
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="password">Password</label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                className={styles.input}
              />
            </div>

            <div className={styles.forgotPassword}>
              <Link href="/admin/forgot-password" className={styles.link}>
                Forgot password?
              </Link>
            </div>

            <button
              type="submit"
              className={styles.loginButton}
              disabled={loading}
            >
              {loading ? 'Logging in...' : 'Log In'}
            </button>
          </form>

          <div className={styles.backToSite}>
            <Link href="/" className={styles.link}>
              ← Back to website
            </Link>
          </div>
        </div>
      </div>
    </>
  )
}
