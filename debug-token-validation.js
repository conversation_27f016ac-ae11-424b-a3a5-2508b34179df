/**
 * Debug script to test token validation patterns
 * Run this in browser console to see what tokens look like
 */

// Current restrictive regex from admin-auth.js
const currentRegex = /^[A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.[A-Za-z0-9-_.+/=]*$/;

// More permissive regex for base64url (JWT standard)
const base64urlRegex = /^[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]*$/;

// Even more permissive regex that allows padding
const permissiveRegex = /^[A-Za-z0-9_-]+(={0,2})\.[A-Za-z0-9_-]+(={0,2})\.[A-Za-z0-9_-]*(={0,2})$/;

// Function to test token format
function testTokenFormat(token) {
  console.log('Testing token:', token.substring(0, 20) + '...');
  console.log('Token length:', token.length);
  console.log('Token parts:', token.split('.').length);
  
  const parts = token.split('.');
  parts.forEach((part, index) => {
    console.log(`Part ${index + 1} length:`, part.length);
    console.log(`Part ${index + 1} sample:`, part.substring(0, 10) + '...');
    
    // Check for invalid characters
    const invalidChars = part.match(/[^A-Za-z0-9_-=]/g);
    if (invalidChars) {
      console.log(`Part ${index + 1} invalid chars:`, invalidChars);
    }
  });
  
  console.log('Current regex passes:', currentRegex.test(token));
  console.log('Base64url regex passes:', base64urlRegex.test(token));
  console.log('Permissive regex passes:', permissiveRegex.test(token));
  console.log('---');
}

// Function to get and test current token
async function debugCurrentToken() {
  try {
    // Try to get token from sessionStorage
    const cachedToken = sessionStorage.getItem('oss_auth_token_cache');
    if (cachedToken) {
      const tokenData = JSON.parse(cachedToken);
      if (tokenData && tokenData.token) {
        console.log('Testing cached token from sessionStorage:');
        testTokenFormat(tokenData.token);
        return;
      }
    }
    
    // Try to get token from Supabase session
    if (window.supabase) {
      const { data, error } = await window.supabase.auth.getSession();
      if (data?.session?.access_token) {
        console.log('Testing token from Supabase session:');
        testTokenFormat(data.session.access_token);
        return;
      }
    }
    
    console.log('No token found to test');
  } catch (error) {
    console.error('Error getting token for testing:', error);
  }
}

// Export for use
window.debugTokenValidation = {
  testTokenFormat,
  debugCurrentToken,
  currentRegex,
  base64urlRegex,
  permissiveRegex
};

console.log('Token validation debug tools loaded. Run debugCurrentToken() to test current token.');
