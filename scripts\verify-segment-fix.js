/**
 * Quick Verification Script for Marketing Segment Fix
 * 
 * This script performs a quick check to verify the infinite re-render fix
 * is properly implemented in the marketing segment components.
 */

const fs = require('fs');
const path = require('path');

// Files to check
const filesToCheck = [
  {
    path: 'pages/admin/marketing/segments/new.js',
    checks: [
      { pattern: /import.*useCallback/, description: 'useCallback import' },
      { pattern: /const handleSegmentQueryChange = useCallback/, description: 'memoized handleSegmentQueryChange' },
      { pattern: /const handleSegmentPreview = useCallback/, description: 'memoized handleSegmentPreview' }
    ]
  },
  {
    path: 'pages/admin/marketing/segments/[id]/edit.js',
    checks: [
      { pattern: /import.*useCallback/, description: 'useCallback import' },
      { pattern: /const handleSegmentQueryChange = useCallback/, description: 'memoized handleSegmentQueryChange' },
      { pattern: /const handleSegmentPreview = useCallback/, description: 'memoized handleSegmentPreview' }
    ]
  },
  {
    path: 'components/admin/marketing/SegmentBuilder.js',
    checks: [
      { pattern: /}, \[groups\]\) \/\/ Removed onChange/, description: 'optimized useEffect dependencies' },
      { pattern: /useEffect\(\(\) => \{[\s\S]*?\}, \[groups\]\)/, description: 'useEffect without onChange dependency' }
    ]
  }
];

// Test results
const results = {
  passed: 0,
  failed: 0,
  errors: []
};

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function assert(condition, message) {
  if (condition) {
    results.passed++;
    log(`PASS: ${message}`, 'success');
  } else {
    results.failed++;
    results.errors.push(message);
    log(`FAIL: ${message}`, 'error');
  }
}

function checkFile(fileInfo) {
  const filePath = path.join(process.cwd(), fileInfo.path);
  
  log(`Checking ${fileInfo.path}...`);
  
  try {
    if (!fs.existsSync(filePath)) {
      assert(false, `File ${fileInfo.path} does not exist`);
      return;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    fileInfo.checks.forEach(check => {
      const matches = check.pattern.test(content);
      assert(matches, `${fileInfo.path}: ${check.description} should be implemented`);
    });
    
  } catch (error) {
    assert(false, `Error checking ${fileInfo.path}: ${error.message}`);
  }
}

function checkReactPatterns() {
  log('Checking for React anti-patterns...');
  
  const segmentBuilderPath = path.join(process.cwd(), 'components/admin/marketing/SegmentBuilder.js');
  
  try {
    const content = fs.readFileSync(segmentBuilderPath, 'utf8');
    
    // Check for problematic patterns
    const hasOnChangeInDeps = /useEffect\([^}]+\}, \[[^\]]*onChange[^\]]*\]/.test(content);
    assert(!hasOnChangeInDeps, 'SegmentBuilder should not have onChange in useEffect dependencies');
    
    // Check for proper useEffect structure
    const hasProperUseEffect = /useEffect\(\(\) => \{[\s\S]*?onChange\(\{ groups \}\)[\s\S]*?\}, \[groups\]\)/.test(content);
    assert(hasProperUseEffect, 'SegmentBuilder should have properly structured useEffect');
    
  } catch (error) {
    assert(false, `Error checking React patterns: ${error.message}`);
  }
}

function checkEnvironmentSetup() {
  log('Checking environment setup...');
  
  // Check if .env.local exists
  const envPath = path.join(process.cwd(), '.env.local');
  assert(fs.existsSync(envPath), '.env.local file should exist');
  
  // Check if required environment variables are set
  require('dotenv').config({ path: '.env.local' });
  
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];
  
  requiredVars.forEach(varName => {
    assert(process.env[varName], `${varName} should be set in environment`);
  });
}

function checkPackageJson() {
  log('Checking package.json dependencies...');
  
  try {
    const packagePath = path.join(process.cwd(), 'package.json');
    const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // Check for React version
    const reactVersion = packageContent.dependencies?.react || packageContent.devDependencies?.react;
    assert(reactVersion, 'React should be listed as a dependency');
    
    // Check for Supabase
    const supabaseClient = packageContent.dependencies?.['@supabase/supabase-js'];
    assert(supabaseClient, '@supabase/supabase-js should be listed as a dependency');
    
  } catch (error) {
    assert(false, `Error checking package.json: ${error.message}`);
  }
}

function runVerification() {
  log('🔍 Starting marketing segment fix verification...');
  log('');
  
  try {
    // Check each file for proper implementation
    filesToCheck.forEach(checkFile);
    
    // Check for React anti-patterns
    checkReactPatterns();
    
    // Check environment setup
    checkEnvironmentSetup();
    
    // Check package dependencies
    checkPackageJson();
    
  } catch (error) {
    assert(false, `Verification failed: ${error.message}`);
  }
  
  // Report results
  log('');
  log('=== VERIFICATION RESULTS ===');
  log(`✅ Passed: ${results.passed}`);
  log(`❌ Failed: ${results.failed}`);
  
  if (results.failed > 0) {
    log('');
    log('❌ Failed checks:');
    results.errors.forEach(error => log(`- ${error}`, 'error'));
    
    log('');
    log('🔧 RECOMMENDED ACTIONS:');
    log('1. Review the failed checks above');
    log('2. Ensure all useCallback implementations are correct');
    log('3. Verify useEffect dependencies are optimized');
    log('4. Check environment variables are properly set');
    
    process.exit(1);
  } else {
    log('');
    log('🎉 All verification checks passed!', 'success');
    log('');
    log('✅ INFINITE RE-RENDER FIX STATUS: VERIFIED');
    log('✅ React patterns: Optimized');
    log('✅ useCallback: Properly implemented');
    log('✅ useEffect: Dependencies optimized');
    log('✅ Environment: Configured correctly');
    log('');
    log('🚀 The marketing segment builder is ready for use!');
    log('');
    log('📋 NEXT STEPS:');
    log('1. Test the segment builder in the browser');
    log('2. Create a test segment to verify functionality');
    log('3. Monitor for any console errors during use');
    
    process.exit(0);
  }
}

// Run verification if called directly
if (require.main === module) {
  runVerification();
}

module.exports = { runVerification, results };
