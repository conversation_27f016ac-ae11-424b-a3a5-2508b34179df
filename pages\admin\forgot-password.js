import { useState } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import { resetPassword } from '@/lib/auth'
import styles from '@/styles/admin/Login.module.css'

export default function ForgotPassword() {
  const [email, setEmail] = useState('')
  const [message, setMessage] = useState(null)
  const [error, setError] = useState(null)
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    setMessage(null)

    const { error } = await resetPassword(email)

    if (error) {
      setError(error.message)
      setLoading(false)
      return
    }

    setMessage('Password reset instructions have been sent to your email.')
    setLoading(false)
  }

  return (
    <>
      <Head>
        <title>Forgot Password | OceanSoulSparkles Admin</title>
        <meta name="description" content="OceanSoulSparkles Admin Forgot Password" />
      </Head>

      <div className={styles.loginContainer}>
        <div className={styles.loginCard}>
          <div className={styles.logoContainer}>
            <img
              src="images\bannerlogo.PNG"
              alt="OceanSoulSparkles Logo"
              className={styles.logo}
            />
          </div>

          <h1 className={styles.title}>Forgot Password</h1>

          {error && <div className={styles.error}>{error}</div>}
          {message && <div className={styles.success}>{message}</div>}

          <form onSubmit={handleSubmit} className={styles.form}>
            <div className={styles.formGroup}>
              <label htmlFor="email">Email</label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className={styles.input}
              />
            </div>

            <button
              type="submit"
              className={styles.loginButton}
              disabled={loading}
            >
              {loading ? 'Sending...' : 'Reset Password'}
            </button>
          </form>

          <div className={styles.backToSite}>
            <Link href="/admin/login" className={styles.link}>
              ← Back to login
            </Link>
          </div>
        </div>
      </div>
    </>
  )
}
