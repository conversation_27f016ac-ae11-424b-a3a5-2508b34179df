const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Configuration
const SITE_URL = 'https://www.oceansoulsparkles.com.au';
const PUBLIC_DIR = path.join(process.cwd(), 'public');
const PAGES_DIR = path.join(process.cwd(), 'pages');
const SITEMAP_PATH = path.join(PUBLIC_DIR, 'sitemap.xml');
const EXCLUDED_PATHS = [
  '_app.js',
  '_document.js',
  '_error.js',
  'api',
  '404.js',
  '500.js',
  'admin' // Exclude admin pages from public sitemap
];

// Get all pages
function getPages() {
  const pages = glob.sync(`${PAGES_DIR}/**/*.js`);

  return pages
    .filter(page => {
      const relativePath = path.relative(PAGES_DIR, page);
      return !EXCLUDED_PATHS.some(excluded => relativePath.includes(excluded));
    })
    .map(page => {
      let route = page
        .replace(PAGES_DIR, '')
        .replace(/\.js$/, '')
        .replace(/\/index$/, '')
        .replace(/\\/g, '/'); // Fix Windows path separators

      if (route === '') {
        route = '/';
      }

      return {
        path: route,
        lastModified: new Date(fs.statSync(page).mtime).toISOString().split('T')[0],
        changeFreq: route === '/' ? 'daily' : 'weekly',
        priority: route === '/' ? '1.0' : '0.8'
      };
    });
}

// Generate sitemap XML
function generateSitemap(pages) {
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${pages.map(page => `  <url>
    <loc>${SITE_URL}${page.path}</loc>
    <lastmod>${page.lastModified}</lastmod>
    <changefreq>${page.changeFreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('\n')}
</urlset>`;

  fs.writeFileSync(SITEMAP_PATH, sitemap);
  console.log(`Sitemap generated at ${SITEMAP_PATH}`);
}

// Main function
function main() {
  const pages = getPages();
  generateSitemap(pages);
}

main();
