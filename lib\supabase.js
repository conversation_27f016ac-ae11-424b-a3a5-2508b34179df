/**
 * Unified Supabase Client
 *
 * This module provides a single source of truth for Supabase authentication
 * across the application, replacing the fragmented approach previously used.
 *
 * It exports:
 * - supabase: The main Supabase client for client-side use
 * - supabaseAdmin: The admin client for server-side use (bypasses RLS)
 * - getAdminClient: A function to get the admin client for server-side use
 */

import { createClient } from '@supabase/supabase-js'

/**
 * Known admin user IDs
 * Centralized list to avoid duplication
 */
export const KNOWN_ADMIN_IDS = [
  '8c59a3bc-a96b-4555-bdc4-6abe905ae761', // <EMAIL>
  'c6080246-db51-485e-8e29-69be7cc86cdb'  // <EMAIL>
];

/**
 * Check if a user ID is a known admin
 *
 * @param {string} userId - User ID to check
 * @returns {boolean} - True if user is a known admin
 */
export function isKnownAdmin(userId) {
  return KNOWN_ADMIN_IDS.includes(userId);
}

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// Default headers for all requests
const DEFAULT_HEADERS = {
  'X-Client-Info': 'ocean-soul-sparkles@1.0.0',
  'X-Protocol-Version': '1.0'
}

/**
 * Custom fetch implementation with timeout
 * Prevents requests from hanging indefinitely
 */
const fetchWithTimeout = async (resource, options = {}) => {
  const controller = new AbortController()
  const timeout = options.timeout || 15000 // Increased to 15 second timeout for better reliability

  // Generate a request ID for logging
  const requestId = Math.random().toString(36).substring(2, 8)

  // Log the request if in development mode
  if (process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_DEBUG_AUTH === 'true') {
    console.log(`[${requestId}] Fetch request to: ${typeof resource === 'string' ? resource : 'Request object'}`)
  }

  const id = setTimeout(() => {
    controller.abort()
    console.error(`[${requestId}] Fetch request timed out after ${timeout}ms`)
  }, timeout)

  try {
    const response = await fetch(resource, {
      ...options,
      signal: controller.signal
    })

    // Log the response status if in development mode
    if (process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_DEBUG_AUTH === 'true') {
      console.log(`[${requestId}] Fetch response status: ${response.status}`)
    }

    return response
  } catch (error) {
    // Log fetch errors
    console.error(`[${requestId}] Fetch error:`, error.name, error.message)
    throw error
  } finally {
    clearTimeout(id)
  }
}

/**
 * Main Supabase client for client-side use
 * Uses consistent configuration for authentication
 */
export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    storageKey: 'oss_auth_token', // Standardized token name
    cookieOptions: {
      path: '/',
      sameSite: 'Lax',
      secure: process.env.NODE_ENV === 'production'
    }
  },
  global: {
    headers: DEFAULT_HEADERS,
    fetch: fetchWithTimeout
  },
  realtime: {
    params: {
      eventsPerSecond: 2 // Reasonable request throttling
    }
  }
})

/**
 * Supabase admin client with service role key
 * This bypasses RLS policies and should only be used server-side
 *
 * Note: This will throw an error if used client-side or if the service role key is not available
 */
export const supabaseAdmin = typeof window === 'undefined' && supabaseServiceKey
  ? createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      },
      global: {
        headers: {
          ...DEFAULT_HEADERS,
          'X-Client-Info': 'ocean-soul-sparkles-admin@1.0.0'
        },
        fetch: fetchWithTimeout
      }
    })
  : null;

/**
 * Get the admin client with service role credentials
 * This should only be used server-side and never exposed to the client
 *
 * @returns {Object} Supabase admin client
 * @throws {Error} If called client-side or if service role key is not available
 */
export const getAdminClient = () => {
  // Generate a request ID for logging
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Getting admin client`);

  // Check environment
  if (typeof window !== 'undefined') {
    console.error(`[${requestId}] Attempted to use admin client on client-side`);
    throw new Error('Admin client can only be used server-side');
  }

  // Check for service key
  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
    console.error(`[${requestId}] SUPABASE_SERVICE_ROLE_KEY is missing`);
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is required for admin operations');
  }

  if (!supabaseServiceKey) {
    console.error(`[${requestId}] supabaseServiceKey is not defined`);
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is not defined');
  }

  // Check for URL
  if (!supabaseUrl) {
    console.error(`[${requestId}] NEXT_PUBLIC_SUPABASE_URL is missing`);
    throw new Error('NEXT_PUBLIC_SUPABASE_URL is required for admin operations');
  }

  try {
    // Create and return the admin client
    console.log(`[${requestId}] Creating admin client with service role key`);
    const client = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      },
      global: {
        headers: {
          ...DEFAULT_HEADERS,
          'X-Client-Info': 'ocean-soul-sparkles-admin@1.0.0'
        },
        fetch: fetchWithTimeout
      }
    });

    console.log(`[${requestId}] Admin client created successfully`);
    return client;
  } catch (error) {
    console.error(`[${requestId}] Error creating admin client:`, error);
    throw new Error(`Failed to create admin client: ${error.message}`);
  }
}

/**
 * Get a Supabase client (either admin or regular depending on environment)
 * This is a convenience function that automatically chooses the appropriate client
 *
 * @returns {Object} Supabase client
 */
export const getClient = () => {
  // Generate a request ID for logging
  const requestId = Math.random().toString(36).substring(2, 8);

  // On server-side, prefer admin client if available
  if (typeof window === 'undefined') {
    console.log(`[${requestId}] Getting client for server-side use`);
    try {
      return getAdminClient();
    } catch (error) {
      console.warn(`[${requestId}] Admin client not available, falling back to regular client:`, error.message);
      return supabase;
    }
  }

  // On client-side, use regular client
  console.log(`[${requestId}] Getting client for client-side use`);
  return supabase;
};

/**
 * Get the current session
 * Convenience method with timeout protection
 *
 * @returns {Promise<Object>} Session data or null
 */
export const getSession = async () => {
  try {
    // Add timeout protection to prevent hanging
    const sessionPromise = supabase.auth.getSession()
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('getSession timeout')), 8000)
    })

    const { data, error } = await Promise.race([sessionPromise, timeoutPromise])

    if (error) {
      console.error('Error getting session:', error)
      return null
    }

    return data.session
  } catch (error) {
    console.error('Exception in getSession:', error)
    return null
  }
}

/**
 * Get the current user
 * Convenience method with role information
 *
 * @returns {Promise<Object>} User data with role or null
 */
export const getCurrentUser = async () => {
  try {
    // Add timeout protection to prevent hanging
    const sessionPromise = getSession()
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('getCurrentUser timeout')), 10000)
    })

    const session = await Promise.race([sessionPromise, timeoutPromise])

    if (!session || !session.user) {
      return { user: null, role: null }
    }

    // Get user role from user_roles table
    const { data: roleData, error: roleError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('id', session.user.id)
      .single()

    if (roleError) {
      console.error('Error fetching user role:', roleError)

      // Fallback for known admin users
      if (isKnownAdmin(session.user.id)) {
        return {
          user: session.user,
          role: 'admin'
        }
      }

      return {
        user: session.user,
        role: null
      }
    }

    return {
      user: session.user,
      role: roleData.role
    }
  } catch (error) {
    console.error('Error in getCurrentUser:', error)
    return { user: null, role: null }
  }
}

/**
 * Get the current user with a specific token
 * Used for admin authentication with a provided token
 *
 * @param {string} token - JWT token
 * @returns {Promise<Object>} User data with role or error
 */
export const getCurrentUserWithToken = async (token) => {
  try {
    if (!token) {
      return { user: null, role: null, error: new Error('No token provided') }
    }

    // Get admin client to verify the token
    const adminClient = getAdminClient()

    // Verify token
    const { data, error } = await adminClient.auth.getUser(token)

    if (error) {
      return { user: null, role: null, error }
    }

    if (!data.user) {
      return { user: null, role: null, error: new Error('Invalid token') }
    }

    // Get user role from user_roles table
    const { data: roleData, error: roleError } = await adminClient
      .from('user_roles')
      .select('role')
      .eq('id', data.user.id)
      .single()

    if (roleError) {
      console.error('Error fetching user role with token:', roleError)

      // Fallback for known admin users
      if (isKnownAdmin(data.user.id)) {
        return {
          user: data.user,
          role: 'admin',
          error: null
        }
      }

      return {
        user: data.user,
        role: null,
        error: roleError
      }
    }

    return {
      user: data.user,
      role: roleData.role,
      error: null
    }
  } catch (error) {
    console.error('Error in getCurrentUserWithToken:', error)
    return { user: null, role: null, error }
  }
}

/**
 * Get the current user from a request object
 * Used for server-side authentication in API routes
 *
 * @param {Object} req - HTTP request object
 * @returns {Promise<Object>} User data with role or throws error
 */
export const getCurrentUserFromRequest = async (req) => {
  try {
    // Generate a request ID for logging
    const requestId = Math.random().toString(36).substring(2, 8);
    console.log(`[${requestId}] Getting current user from request`);

    // Extract token from Authorization header
    let token = null;
    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7).trim();
      console.log(`[${requestId}] Token extracted from Authorization header`);
    }

    // Fallback to cookies if no header token
    if (!token && req.cookies) {
      token = req.cookies['sb-access-token'] ||
              req.cookies['supabase-auth-token'] ||
              req.cookies['oss_auth_token'];
      if (token) {
        console.log(`[${requestId}] Token extracted from cookies`);
      }
    }

    // Fallback to X-Auth-Token header
    if (!token && req.headers['x-auth-token']) {
      token = req.headers['x-auth-token'];
      console.log(`[${requestId}] Token extracted from X-Auth-Token header`);
    }

    if (!token) {
      console.warn(`[${requestId}] No authentication token found`);
      throw new Error('No authentication token provided');
    }

    // Validate token and get user/role
    console.log(`[${requestId}] Validating token with getCurrentUserWithToken`);
    const result = await getCurrentUserWithToken(token);

    if (result.error) {
      console.error(`[${requestId}] Token validation failed:`, result.error.message);
      throw result.error;
    }

    if (!result.user) {
      console.warn(`[${requestId}] No user found for token`);
      throw new Error('Invalid authentication token');
    }

    console.log(`[${requestId}] Authentication successful for user: ${result.user.email}, role: ${result.role}`);
    return { user: result.user, role: result.role };
  } catch (error) {
    console.error('Error in getCurrentUserFromRequest:', error);
    throw error;
  }
}

/**
 * Authentication methods for common operations
 */
export const auth = {
  /**
   * Sign in with email and password
   *
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} Auth response
   */
  async signIn(email, password) {
    return await supabase.auth.signInWithPassword({ email, password })
  },

  /**
   * Sign out current user
   *
   * @returns {Promise<Object>} Auth response
   */
  async signOut() {
    return await supabase.auth.signOut()
  },

  /**
   * Get current session
   *
   * @returns {Promise<Object>} Session object
   */
  async getSession() {
    return await supabase.auth.getSession()
  },

  /**
   * Get current user
   *
   * @returns {Promise<Object>} User object
   */
  async getUser() {
    return await supabase.auth.getUser()
  }
}

// Default export for backward compatibility
export default supabase

