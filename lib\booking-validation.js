/**
 * Booking Validation and Error Handling Library
 * 
 * This module provides comprehensive validation and error handling
 * for the booking system to ensure data integrity and user experience.
 */

/**
 * Validate booking data before database insertion
 * @param {Object} bookingData - The booking data to validate
 * @returns {Object} - Validation result with errors if any
 */
export function validateBookingData(bookingData) {
  const errors = [];
  const warnings = [];

  // Required field validation
  const requiredFields = ['customer_id', 'service_id', 'start_time', 'end_time', 'status'];
  
  for (const field of requiredFields) {
    if (!bookingData[field]) {
      errors.push(`${field} is required`);
    }
  }

  // Date validation
  if (bookingData.start_time && bookingData.end_time) {
    const startTime = new Date(bookingData.start_time);
    const endTime = new Date(bookingData.end_time);
    const now = new Date();

    // Check if dates are valid
    if (isNaN(startTime.getTime())) {
      errors.push('Invalid start_time format');
    }
    if (isNaN(endTime.getTime())) {
      errors.push('Invalid end_time format');
    }

    // Check if start time is before end time
    if (startTime >= endTime) {
      errors.push('start_time must be before end_time');
    }

    // Check if booking is in the past (with 1 hour grace period)
    const gracePeriod = 60 * 60 * 1000; // 1 hour in milliseconds
    if (startTime < new Date(now.getTime() - gracePeriod)) {
      warnings.push('Booking is scheduled in the past');
    }

    // Check if booking is too far in the future (1 year)
    const oneYear = 365 * 24 * 60 * 60 * 1000;
    if (startTime > new Date(now.getTime() + oneYear)) {
      warnings.push('Booking is scheduled more than 1 year in advance');
    }

    // Check booking duration (minimum 15 minutes, maximum 8 hours)
    const duration = endTime - startTime;
    const minDuration = 15 * 60 * 1000; // 15 minutes
    const maxDuration = 8 * 60 * 60 * 1000; // 8 hours

    if (duration < minDuration) {
      errors.push('Booking duration must be at least 15 minutes');
    }
    if (duration > maxDuration) {
      warnings.push('Booking duration exceeds 8 hours');
    }
  }

  // Status validation
  const validStatuses = ['confirmed', 'pending', 'canceled'];
  if (bookingData.status && !validStatuses.includes(bookingData.status)) {
    errors.push(`Invalid status. Must be one of: ${validStatuses.join(', ')}`);
  }

  // Location validation
  if (bookingData.location && bookingData.location.length > 255) {
    errors.push('Location must be 255 characters or less');
  }

  // Notes validation
  if (bookingData.notes && bookingData.notes.length > 1000) {
    errors.push('Notes must be 1000 characters or less');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validate customer data before database insertion
 * @param {Object} customerData - The customer data to validate
 * @returns {Object} - Validation result with errors if any
 */
export function validateCustomerData(customerData) {
  const errors = [];
  const warnings = [];

  // Required field validation
  const requiredFields = ['name', 'email'];
  
  for (const field of requiredFields) {
    if (!customerData[field]) {
      errors.push(`${field} is required`);
    }
  }

  // Email validation
  if (customerData.email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(customerData.email)) {
      errors.push('Invalid email format');
    }
    if (customerData.email.length > 255) {
      errors.push('Email must be 255 characters or less');
    }
  }

  // Name validation
  if (customerData.name) {
    if (customerData.name.length < 2) {
      errors.push('Name must be at least 2 characters long');
    }
    if (customerData.name.length > 100) {
      errors.push('Name must be 100 characters or less');
    }
  }

  // Phone validation
  if (customerData.phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (!phoneRegex.test(customerData.phone.replace(/[\s\-\(\)]/g, ''))) {
      warnings.push('Phone number format may be invalid');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Check for booking conflicts with existing bookings
 * @param {Object} supabaseClient - Supabase client instance
 * @param {Object} bookingData - The booking data to check
 * @param {string} excludeBookingId - Booking ID to exclude from conflict check (for updates)
 * @returns {Promise<Object>} - Conflict check result
 */
export async function checkBookingConflicts(supabaseClient, bookingData, excludeBookingId = null) {
  try {
    const { start_time, end_time, service_id } = bookingData;

    // Build query to find overlapping bookings
    let query = supabaseClient
      .from('bookings')
      .select('id, start_time, end_time, customers(name), services(name)')
      .eq('service_id', service_id)
      .neq('status', 'canceled')
      .or(`and(start_time.lte.${start_time},end_time.gt.${start_time}),and(start_time.lt.${end_time},end_time.gte.${end_time}),and(start_time.gte.${start_time},end_time.lte.${end_time})`);

    // Exclude current booking if updating
    if (excludeBookingId) {
      query = query.neq('id', excludeBookingId);
    }

    const { data: conflicts, error } = await query;

    if (error) {
      throw error;
    }

    return {
      hasConflicts: conflicts && conflicts.length > 0,
      conflicts: conflicts || [],
      conflictCount: conflicts ? conflicts.length : 0
    };
  } catch (error) {
    console.error('Error checking booking conflicts:', error);
    return {
      hasConflicts: false,
      conflicts: [],
      conflictCount: 0,
      error: error.message
    };
  }
}

/**
 * Sanitize and normalize booking data
 * @param {Object} rawData - Raw booking data from form
 * @returns {Object} - Sanitized booking data
 */
export function sanitizeBookingData(rawData) {
  const sanitized = {};

  // Copy and sanitize basic fields
  if (rawData.customer_id) sanitized.customer_id = rawData.customer_id.trim();
  if (rawData.service_id) sanitized.service_id = rawData.service_id.trim();
  if (rawData.status) sanitized.status = rawData.status.toLowerCase().trim();
  
  // Sanitize and validate dates
  if (rawData.start_time) {
    const startTime = new Date(rawData.start_time);
    if (!isNaN(startTime.getTime())) {
      sanitized.start_time = startTime.toISOString();
    }
  }
  
  if (rawData.end_time) {
    const endTime = new Date(rawData.end_time);
    if (!isNaN(endTime.getTime())) {
      sanitized.end_time = endTime.toISOString();
    }
  }

  // Sanitize text fields
  if (rawData.location) {
    sanitized.location = rawData.location.trim().substring(0, 255);
  }
  
  if (rawData.notes) {
    sanitized.notes = rawData.notes.trim().substring(0, 1000);
  }

  return sanitized;
}

/**
 * Sanitize and normalize customer data
 * @param {Object} rawData - Raw customer data from form
 * @returns {Object} - Sanitized customer data
 */
export function sanitizeCustomerData(rawData) {
  const sanitized = {};

  // Sanitize basic fields
  if (rawData.name) {
    sanitized.name = rawData.name.trim().substring(0, 100);
  }
  
  if (rawData.email) {
    sanitized.email = rawData.email.toLowerCase().trim().substring(0, 255);
  }
  
  if (rawData.phone) {
    // Remove common phone formatting characters
    sanitized.phone = rawData.phone.replace(/[\s\-\(\)]/g, '').trim();
  }

  // Sanitize address fields
  if (rawData.address) sanitized.address = rawData.address.trim().substring(0, 255);
  if (rawData.city) sanitized.city = rawData.city.trim().substring(0, 100);
  if (rawData.state) sanitized.state = rawData.state.trim().substring(0, 100);
  if (rawData.postal_code) sanitized.postal_code = rawData.postal_code.trim().substring(0, 20);
  if (rawData.country) sanitized.country = rawData.country.trim().substring(0, 100);

  // Handle boolean fields
  if (rawData.marketing_consent !== undefined) {
    sanitized.marketing_consent = Boolean(rawData.marketing_consent);
  }

  if (rawData.vip !== undefined) {
    sanitized.vip = Boolean(rawData.vip);
  }

  return sanitized;
}

/**
 * Format error messages for user display
 * @param {Array} errors - Array of error messages
 * @param {Array} warnings - Array of warning messages
 * @returns {Object} - Formatted error response
 */
export function formatValidationErrors(errors, warnings = []) {
  return {
    success: false,
    errors: errors.map(error => ({
      message: error,
      type: 'error'
    })),
    warnings: warnings.map(warning => ({
      message: warning,
      type: 'warning'
    })),
    hasErrors: errors.length > 0,
    hasWarnings: warnings.length > 0
  };
}

/**
 * Create a standardized success response
 * @param {Object} data - Success data
 * @param {string} message - Success message
 * @returns {Object} - Formatted success response
 */
export function formatSuccessResponse(data, message = 'Operation completed successfully') {
  return {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  };
}
