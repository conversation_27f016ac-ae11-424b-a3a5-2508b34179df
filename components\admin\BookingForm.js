import { useState, useEffect } from 'react';
import { sendBookingNotification } from '@/lib/notifications';
import styles from '@/styles/admin/BookingForm.module.css';

/**
 * BookingForm component for creating and editing bookings
 *
 * @param {Object} props - Component props
 * @param {Object} props.booking - Booking object for editing (null for new bookings)
 * @param {Object} props.initialSlot - Initial time slot for new bookings
 * @param {Function} props.onSave - Function to call when booking is saved
 * @param {Function} props.onCancel - Function to call when form is canceled
 * @returns {JSX.Element}
 */
export default function BookingForm({ booking, initialSlot, onSave, onCancel }) {
  const [formData, setFormData] = useState({
    customer_id: '',
    service_id: '',
    start_time: '',
    end_time: '',
    status: 'pending',
    location: '',
    notes: ''
  });

  const [customers, setCustomers] = useState([]);
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isNewCustomer, setIsNewCustomer] = useState(false);
  const [newCustomer, setNewCustomer] = useState({
    name: '',
    email: '',
    phone: ''
  });

  // Initialize form with booking data if editing or initialSlot if creating
  useEffect(() => {
    if (booking) {
      // Format dates for form inputs
      const startTime = new Date(booking.start_time);
      const endTime = new Date(booking.end_time);

      console.log('BookingForm: Initializing with booking data:', {
        bookingId: booking.id,
        customerId: booking.customer_id,
        customerName: booking.customerName,
        customerEmail: booking.customerEmail
      });

      setFormData({
        customer_id: booking.customer_id,
        service_id: booking.service_id,
        start_time: formatDateTimeForInput(startTime),
        end_time: formatDateTimeForInput(endTime),
        status: booking.status,
        location: booking.location || '',
        notes: booking.notes || ''
      });
    } else if (initialSlot) {
      // Use the selected slot for new bookings
      const startTime = new Date(initialSlot.start);
      const endTime = new Date(initialSlot.end);

      setFormData(prevData => ({
        ...prevData,
        start_time: formatDateTimeForInput(startTime),
        end_time: formatDateTimeForInput(endTime)
      }));
    } else {
      // Set default times for new bookings
      const now = new Date();
      const startTime = new Date(now.setMinutes(Math.ceil(now.getMinutes() / 30) * 30));
      const endTime = new Date(startTime.getTime() + 60 * 60 * 1000); // 1 hour later

      setFormData(prevData => ({
        ...prevData,
        start_time: formatDateTimeForInput(startTime),
        end_time: formatDateTimeForInput(endTime)
      }));
    }
  }, [booking, initialSlot]);

  // Re-apply booking data when customers are loaded (to ensure customer_id is preserved)
  useEffect(() => {
    if (booking && customers.length > 0 && formData.customer_id !== booking.customer_id) {
      console.log('BookingForm: Re-applying customer_id after customers loaded:', {
        bookingCustomerId: booking.customer_id,
        currentFormCustomerId: formData.customer_id,
        customersCount: customers.length
      });

      // Ensure the customer_id is set correctly when customers list is loaded
      setFormData(prevData => ({
        ...prevData,
        customer_id: booking.customer_id
      }));
    }
  }, [booking, customers, formData.customer_id]);

  // Fetch customers and services
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch customers from the API
        const customersResponse = await fetch('/api/admin/customers');
        if (!customersResponse.ok) {
          throw new Error(`Failed to fetch customers: ${customersResponse.statusText}`);
        }
        const { customers: customersData } = await customersResponse.json();

        // Fetch services from the API
        const servicesResponse = await fetch('/api/admin/services');
        if (!servicesResponse.ok) {
          throw new Error(`Failed to fetch services: ${servicesResponse.statusText}`);
        }
        const { services: servicesData } = await servicesResponse.json();

        console.log('BookingForm: Loaded customers:', customersData?.length || 0);
        console.log('BookingForm: Loaded services:', servicesData?.length || 0);

        setCustomers(customersData || []);
        setServices(servicesData || []);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Failed to load customers or services: ' + error.message);
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Update end time when service or start time changes
  useEffect(() => {
    if (formData.service_id && formData.start_time) {
      const service = services.find(s => s.id === formData.service_id);
      if (service) {
        const startTime = new Date(formData.start_time);
        const endTime = new Date(startTime.getTime() + service.duration * 60 * 1000);
        setFormData({
          ...formData,
          end_time: formatDateTimeForInput(endTime)
        });
      }
    }
  }, [formData.service_id, formData.start_time, services]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle new customer input changes
  const handleNewCustomerChange = (e) => {
    const { name, value } = e.target;
    setNewCustomer({
      ...newCustomer,
      [name]: value
    });
  };

  // Format date for datetime-local input
  function formatDateTimeForInput(date) {
    return date.toISOString().slice(0, 16);
  }

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      let customerId = formData.customer_id;

      // Create new customer if needed
      if (isNewCustomer) {
        if (!newCustomer.name || !newCustomer.email) {
          setError('Customer name and email are required');
          setLoading(false);
          return;
        }

        // Create new customer via API
        const customerResponse = await fetch('/api/admin/customers', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: newCustomer.name,
            email: newCustomer.email,
            phone: newCustomer.phone
          }),
        });

        if (!customerResponse.ok) {
          const errorData = await customerResponse.json();
          throw new Error(errorData.error || 'Failed to create customer');
        }

        const { customer: customerData } = await customerResponse.json();

        customerId = customerData.id;
      }

      if (!customerId || !formData.service_id || !formData.start_time || !formData.end_time) {
        setError('Customer, service, start time, and end time are required');
        setLoading(false);
        return;
      }

      const bookingData = {
        customer_id: customerId,
        service_id: formData.service_id,
        start_time: new Date(formData.start_time).toISOString(),
        end_time: new Date(formData.end_time).toISOString(),
        status: formData.status,
        location: formData.location,
        notes: formData.notes
      };

      if (booking) {
        // Update existing booking via API
        const updateResponse = await fetch(`/api/admin/bookings/${booking.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(bookingData),
        });

        if (!updateResponse.ok) {
          const errorData = await updateResponse.json();
          throw new Error(errorData.error || 'Failed to update booking');
        }

        const updateData = await updateResponse.json();
        const updatedBooking = updateData.booking || updateData;

        // Send notification if status changed
        if (formData.status !== booking.status) {
          await sendBookingNotification({
            bookingId: booking.id,
            customerId: customerId,
            status: formData.status,
            startTime: formData.start_time,
            serviceName: services.find(s => s.id === formData.service_id)?.name || 'Service'
          });
        }

        onSave(updatedBooking);
      } else {
        // Create new booking via API
        const createResponse = await fetch('/api/admin/bookings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(bookingData),
        });

        if (!createResponse.ok) {
          const errorData = await createResponse.json();
          throw new Error(errorData.error || 'Failed to create booking');
        }

        const { booking: newBooking } = await createResponse.json();

        // Send notification
        await sendBookingNotification({
          bookingId: newBooking.id,
          customerId: customerId,
          status: formData.status,
          startTime: formData.start_time,
          serviceName: services.find(s => s.id === formData.service_id)?.name || 'Service'
        });

        onSave(newBooking);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error saving booking:', error);
      setError(error.message);
      setLoading(false);
    }
  };

  return (
    <div className={styles.bookingFormContainer}>
      <h2 className={styles.formTitle}>
        {booking ? 'Edit Booking' : 'Create New Booking'}
      </h2>

      {error && (
        <div className={styles.error}>{error}</div>
      )}

      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.formGroup}>
          <label htmlFor="customer">Customer</label>
          {!isNewCustomer ? (
            <>
              <select
                id="customer"
                name="customer_id"
                value={formData.customer_id}
                onChange={handleChange}
                disabled={loading}
                required
              >
                <option value="">Select a customer</option>
                {customers.map(customer => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name} ({customer.email})
                  </option>
                ))}
                {/* Show selected customer even if not in the loaded list yet (for editing) */}
                {booking && formData.customer_id && !customers.find(c => c.id === formData.customer_id) && (
                  <option key={formData.customer_id} value={formData.customer_id}>
                    {booking.customerName} ({booking.customerEmail}) - Loading...
                  </option>
                )}
              </select>
              <button
                type="button"
                className={styles.linkButton}
                onClick={() => setIsNewCustomer(true)}
              >
                Add New Customer
              </button>
            </>
          ) : (
            <div className={styles.newCustomerForm}>
              <div className={styles.formGroup}>
                <label htmlFor="customerName">Name</label>
                <input
                  type="text"
                  id="customerName"
                  name="name"
                  value={newCustomer.name}
                  onChange={handleNewCustomerChange}
                  disabled={loading}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label htmlFor="customerEmail">Email</label>
                <input
                  type="email"
                  id="customerEmail"
                  name="email"
                  value={newCustomer.email}
                  onChange={handleNewCustomerChange}
                  disabled={loading}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label htmlFor="customerPhone">Phone</label>
                <input
                  type="tel"
                  id="customerPhone"
                  name="phone"
                  value={newCustomer.phone}
                  onChange={handleNewCustomerChange}
                  disabled={loading}
                />
              </div>
              <button
                type="button"
                className={styles.linkButton}
                onClick={() => setIsNewCustomer(false)}
              >
                Select Existing Customer
              </button>
            </div>
          )}
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="service">Service</label>
          <select
            id="service"
            name="service_id"
            value={formData.service_id}
            onChange={handleChange}
            disabled={loading}
            required
          >
            <option value="">Select a service</option>
            {services.map(service => (
              <option key={service.id} value={service.id}>
                {service.name} (${service.price} - {service.duration} min)
              </option>
            ))}
          </select>
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label htmlFor="start_time">Start Time</label>
            <input
              type="datetime-local"
              id="start_time"
              name="start_time"
              value={formData.start_time}
              onChange={handleChange}
              disabled={loading}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="end_time">End Time</label>
            <input
              type="datetime-local"
              id="end_time"
              name="end_time"
              value={formData.end_time}
              onChange={handleChange}
              disabled={loading}
              required
            />
          </div>
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="status">Status</label>
          <select
            id="status"
            name="status"
            value={formData.status}
            onChange={handleChange}
            disabled={loading}
            required
          >
            <option value="pending">Pending</option>
            <option value="confirmed">Confirmed</option>
            <option value="canceled">Canceled</option>
          </select>
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="location">Location</label>
          <input
            type="text"
            id="location"
            name="location"
            value={formData.location}
            onChange={handleChange}
            disabled={loading}
          />
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="notes">Notes</label>
          <textarea
            id="notes"
            name="notes"
            value={formData.notes}
            onChange={handleChange}
            disabled={loading}
            rows={4}
          />
        </div>

        <div className={styles.formActions}>
          <button
            type="button"
            className={styles.cancelButton}
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={styles.saveButton}
            disabled={loading}
          >
            {loading ? 'Saving...' : (booking ? 'Update Booking' : 'Create Booking')}
          </button>
        </div>
      </form>
    </div>
  );
}
