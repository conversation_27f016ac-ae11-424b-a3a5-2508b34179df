import { useState } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import supabase from '@/lib/supabase';
import styles from '@/styles/admin/Login.module.css'

export default function ManualSetRole() {
  const [userId, setUserId] = useState('8c59a3bc-a96b-4555-bdc4-6abe905ae761')
  const [role, setRole] = useState('admin')
  const [message, setMessage] = useState(null)
  const [error, setError] = useState(null)
  const [loading, setLoading] = useState(false)
  const [showInstructions, setShowInstructions] = useState(false)

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    setMessage(null)

    if (!userId) {
      setError('User ID is required')
      setLoading(false)
      return
    }

    try {
      // Use Supabase client directly
      const client = supabase;
      // Try to insert directly with the client-side key
      // This will only work if RLS policies allow it
      console.log(`Attempting to set role for user ID: ${userId} to ${role}`)

      // First try to delete any existing role
      const { error: deleteError } = await client
        .from('user_roles')
        .delete()
        .eq('id', userId)

      if (deleteError) {
        console.log('Delete error:', deleteError)
        // Continue anyway as the record might not exist
      }

      // Now insert the new role
      const { data, error: insertError } = await client
        .from('user_roles')
        .insert([{ id: userId, role }])

      if (insertError) {
        console.error('Insert error:', insertError)
        throw new Error(insertError.message || 'Failed to set user role')
      }

      setMessage(`Successfully set role to '${role}' for user ${userId}`)
      setLoading(false)
    } catch (err) {
      console.error('Error setting role:', err)
      setError(err.message || 'Failed to set user role')
      setLoading(false)
      // Show instructions if this fails
      setShowInstructions(true)
    }
  }

  return (
    <>
      <Head>
        <title>Manual Set User Role | OceanSoulSparkles Admin</title>
        <meta name="description" content="Set user role for OceanSoulSparkles Admin" />
      </Head>

      <div className={styles.loginContainer}>
        <div className={styles.loginCard}>
          <div className={styles.logoContainer}>
            <img
              src="images\bannerlogo.PNG"
              alt="OceanSoulSparkles Logo"
              className={styles.logo}
            />
          </div>

          <h1 className={styles.title}>Manual Set User Role</h1>
          <p style={{ textAlign: 'center', marginBottom: '20px' }}>
            This page attempts to set a role directly using the client-side key
          </p>

          {error && <div className={styles.error}>{error}</div>}
          {message && <div className={styles.success}>{message}</div>}

          <form onSubmit={handleSubmit} className={styles.form}>
            <div className={styles.formGroup}>
              <label htmlFor="userId">User ID</label>
              <input
                id="userId"
                type="text"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                required
                className={styles.input}
                placeholder="e.g. 8c59a3bc-a96b-4555-bdc4-6abe905ae761"
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="role">Role</label>
              <select
                id="role"
                value={role}
                onChange={(e) => setRole(e.target.value)}
                className={styles.input}
              >
                <option value="admin">Admin</option>
                <option value="staff">Staff</option>
                <option value="user">User</option>
              </select>
            </div>

            <button
              type="submit"
              className={styles.loginButton}
              disabled={loading}
            >
              {loading ? 'Setting Role...' : 'Set Role'}
            </button>
          </form>

          {showInstructions && (
            <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
              <h3 style={{ marginTop: '0' }}>Alternative Instructions</h3>
              <p>If the above method fails due to RLS policies, follow these steps:</p>
              <ol style={{ paddingLeft: '20px', textAlign: 'left' }}>
                <li>Log in to your <a href="https://app.supabase.com" target="_blank" rel="noopener noreferrer">Supabase Dashboard</a></li>
                <li>Select your project</li>
                <li>Go to the SQL Editor</li>
                <li>Run the following SQL:</li>
              </ol>
              <pre style={{
                backgroundColor: '#282c34',
                color: '#abb2bf',
                padding: '10px',
                borderRadius: '5px',
                overflowX: 'auto',
                fontSize: '12px'
              }}>
{`-- Delete any existing role
DELETE FROM user_roles WHERE id = '${userId}';

-- Insert new role
INSERT INTO user_roles (id, role)
VALUES ('${userId}', '${role}');

-- Verify
SELECT * FROM user_roles WHERE id = '${userId}';`}
              </pre>
            </div>
          )}

          <div className={styles.backToSite}>
            <Link href="/admin/login" className={styles.link}>
              ← Back to login
            </Link>
          </div>
        </div>
      </div>
    </>
  )
}
