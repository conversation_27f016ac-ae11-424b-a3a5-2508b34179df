/**
 * Test script for the new API endpoints
 * Run this with: node test-api-endpoints.js
 * 
 * This script tests the three new API endpoints:
 * 1. GET /api/admin/customers/{customerId}/bookings
 * 2. GET /api/admin/bookings/{bookingId}/communications
 * 3. PUT /api/admin/bookings/{bookingId}
 */

const BASE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

// Mock authentication token (replace with actual token in testing)
const AUTH_TOKEN = 'your-auth-token-here';

async function testCustomerBookingsAPI() {
  console.log('\n🧪 Testing Customer Bookings API...');
  
  try {
    // You'll need to replace this with an actual customer ID from your database
    const customerId = 'test-customer-id';
    
    const response = await fetch(`${BASE_URL}/api/admin/customers/${customerId}/bookings`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Customer Bookings API working');
      console.log(`Found ${data.bookings?.length || 0} bookings for customer`);
      console.log(`Customer: ${data.customer?.name}`);
      console.log(`Total Revenue: $${data.stats?.totalRevenue || 0}`);
    } else {
      const error = await response.json();
      console.log('❌ Customer Bookings API failed');
      console.log('Error:', error);
    }
  } catch (error) {
    console.log('❌ Customer Bookings API error:', error.message);
  }
}

async function testBookingCommunicationsAPI() {
  console.log('\n🧪 Testing Booking Communications API...');
  
  try {
    // You'll need to replace this with an actual booking ID from your database
    const bookingId = 'test-booking-id';
    
    const response = await fetch(`${BASE_URL}/api/admin/bookings/${bookingId}/communications`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Booking Communications API working');
      console.log(`Found ${data.communications?.length || 0} communications`);
      console.log(`Customer: ${data.booking?.customer_name}`);
    } else {
      const error = await response.json();
      console.log('❌ Booking Communications API failed');
      console.log('Error:', error);
    }
  } catch (error) {
    console.log('❌ Booking Communications API error:', error.message);
  }
}

async function testBookingUpdateAPI() {
  console.log('\n🧪 Testing Booking Update API...');
  
  try {
    // You'll need to replace this with an actual booking ID from your database
    const bookingId = 'test-booking-id';
    
    // First, get the current booking
    const getResponse = await fetch(`${BASE_URL}/api/admin/bookings/${bookingId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`GET Status: ${getResponse.status}`);
    
    if (getResponse.ok) {
      const bookingData = await getResponse.json();
      console.log('✅ Booking GET API working');
      console.log(`Booking: ${bookingData.booking?.booking_reference || bookingData.booking?.id}`);
      console.log(`Current Status: ${bookingData.booking?.status}`);
      
      // Test updating the booking (just update internal notes to avoid changing important data)
      const updateResponse = await fetch(`${BASE_URL}/api/admin/bookings/${bookingId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${AUTH_TOKEN}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          internal_notes: `API test update at ${new Date().toISOString()}`
        })
      });

      console.log(`PUT Status: ${updateResponse.status}`);
      
      if (updateResponse.ok) {
        const updatedData = await updateResponse.json();
        console.log('✅ Booking UPDATE API working');
        console.log(`Updated booking: ${updatedData.booking?.id}`);
      } else {
        const error = await updateResponse.json();
        console.log('❌ Booking UPDATE API failed');
        console.log('Error:', error);
      }
    } else {
      const error = await getResponse.json();
      console.log('❌ Booking GET API failed');
      console.log('Error:', error);
    }
  } catch (error) {
    console.log('❌ Booking Update API error:', error.message);
  }
}

async function testCreateCommunication() {
  console.log('\n🧪 Testing Create Communication API...');
  
  try {
    // You'll need to replace this with an actual booking ID from your database
    const bookingId = 'test-booking-id';
    
    const response = await fetch(`${BASE_URL}/api/admin/bookings/${bookingId}/communications`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        communication_type: 'system',
        direction: 'outbound',
        subject: 'API Test Communication',
        content: 'This is a test communication created via API',
        status: 'sent'
      })
    });

    console.log(`Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Create Communication API working');
      console.log(`Created communication: ${data.communication?.id}`);
    } else {
      const error = await response.json();
      console.log('❌ Create Communication API failed');
      console.log('Error:', error);
    }
  } catch (error) {
    console.log('❌ Create Communication API error:', error.message);
  }
}

async function runAllTests() {
  console.log('🚀 Starting API Endpoint Tests...');
  console.log(`Base URL: ${BASE_URL}`);
  
  if (AUTH_TOKEN === 'your-auth-token-here') {
    console.log('\n⚠️  WARNING: Please update AUTH_TOKEN with a valid authentication token');
    console.log('⚠️  WARNING: Please update customer and booking IDs with actual values from your database');
  }
  
  await testCustomerBookingsAPI();
  await testBookingCommunicationsAPI();
  await testBookingUpdateAPI();
  await testCreateCommunication();
  
  console.log('\n✅ All API endpoint tests completed!');
  console.log('\n📝 Next Steps:');
  console.log('1. Update the test IDs with real values from your database');
  console.log('2. Get a valid authentication token');
  console.log('3. Run the tests again to verify functionality');
  console.log('4. Test the enhanced booking system in the admin interface');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testCustomerBookingsAPI,
  testBookingCommunicationsAPI,
  testBookingUpdateAPI,
  testCreateCommunication
};
